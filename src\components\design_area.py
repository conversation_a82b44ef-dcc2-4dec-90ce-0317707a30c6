# -*- coding: utf-8 -*-
"""
设计区域组件
提供图形化设计和表格数据展示功能
"""

import math
from PyQt5.QtWidgets import (QWidget, QGraphicsView, QGraphicsScene, 
                            QGraphicsItem, QGraphicsEllipseItem, QGraphicsLineItem,
                            QGraphicsTextItem, QVBoxLayout, QHBoxLayout, QStackedWidget,
                            QTableWidget, QTableWidgetItem, QHeaderView, QDialog,
                            QMessageBox, QTextEdit, QLabel, QMenu, QAction,
                            QGroupBox, QFormLayout)
from PyQt5.QtCore import Qt, QRectF, QPointF, pyqtSignal, QTimer
from PyQt5.QtGui import QPen, QBrush, QPainter, QFont, QColor

class DesignGraphicsView(QGraphicsView):
    """设计图形视图"""
    
    mouse_moved = pyqtSignal(QPointF)  # 鼠标移动信号
    
    def __init__(self, design_area, parent=None):
        super().__init__(parent)
        self.design_area = design_area  # 保存对DesignArea的引用
        self.setRenderHint(QPainter.Antialiasing)
        self.setDragMode(QGraphicsView.RubberBandDrag)
        self.setResizeAnchor(QGraphicsView.AnchorUnderMouse)
        self.setTransformationAnchor(QGraphicsView.AnchorUnderMouse)
        
        # 设置滚轮缩放
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 当前操作模式
        self.current_mode = "select"
        
        # 缩放比例
        self.zoom_factor = 1.0
        
        # 炮孔操作模式
        self.hole_operation_mode = None  # add, delete, edit, move
        
        # 精细化设计模式
        self.detailed_design_mode = None  # rock, explosive, diameter, spacing
        self.detailed_design_settings = {}
        
        # 绘制模式
        self.drawing_mode = None  # work_surface, boundary, text, reference_point
        self.drawing_points = []  # 当前绘制的点列表
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        scene_pos = self.mapToScene(event.pos())
        self.mouse_moved.emit(scene_pos)
        super().mouseMoveEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        scene_pos = self.mapToScene(event.pos())
        print(f"=== DesignGraphicsView.mousePressEvent ===")
        print(f"鼠标按键: {event.button()}")
        print(f"场景坐标: ({scene_pos.x():.2f}, {scene_pos.y():.2f})")
        print(f"检查精细化设计模式: hasattr(self, 'detailed_design_mode') = {hasattr(self, 'detailed_design_mode')}")
        if hasattr(self, 'detailed_design_mode'):
            print(f"self.detailed_design_mode = {self.detailed_design_mode}")
        print(f"检查炮孔操作模式: self.hole_operation_mode = {getattr(self, 'hole_operation_mode', 'None')}")
        
        if event.button() == Qt.LeftButton:
            design_area = self.design_area
            print(f"设计区域对象: {design_area}")
            print(f"炸药量编辑模式标志存在: {hasattr(design_area, 'explosive_amount_edit_mode')}")
            if hasattr(design_area, 'explosive_amount_edit_mode'):
                print(f"炸药量编辑模式值: {design_area.explosive_amount_edit_mode}")
            
            # 检查是否在炸药量编辑模式 - 最高优先级
            if hasattr(design_area, 'explosive_amount_edit_mode') and design_area.explosive_amount_edit_mode:
                print("炸药量编辑模式处理...")
                item = self.scene().itemAt(scene_pos, self.transform())
                print(f"点击的场景项目: {item}")
                if isinstance(item, BlastHoleItem):
                    print(f"点击炮孔: {item.hole_id}")
                    # 获取主窗口并调用炸药量编辑对话框
                    main_window = design_area.parent()  # 设计区域 -> 主窗口
                    print(f"主窗口对象: {main_window}")
                    print(f"主窗口是否有炸药量编辑方法: {hasattr(main_window, 'open_explosive_amount_dialog') if main_window else 'None'}")
                    if main_window and hasattr(main_window, 'open_explosive_amount_dialog'):
                        print("调用主窗口的炸药量编辑对话框...")
                        main_window.open_explosive_amount_dialog(item)
                    else:
                        print("ERROR: 未找到主窗口或炸药量编辑方法")
                else:
                    print("点击的不是炮孔，继续等待...")
                return
            
            # 检查是否在智能布孔模式
            if hasattr(design_area, 'layout_mode') and design_area.layout_mode:
                print("智能布孔模式处理...")
                if design_area.handle_layout_click(scene_pos):
                    return
            
            # 处理绘制模式 (左键点击)
            if hasattr(self, 'drawing_mode') and self.drawing_mode:
                print(f"绘制模式激活: {self.drawing_mode}")
                self.handle_drawing_click(scene_pos)
                return
            
            # 处理精细化设计模式
            if hasattr(self, 'detailed_design_mode') and self.detailed_design_mode:
                print(f"精细化设计模式激活: {self.detailed_design_mode}")
                print("调用 handle_detailed_design_click...")
                self.handle_detailed_design_click(scene_pos)
                return
            
            # 处理炮孔操作模式
            if self.hole_operation_mode:
                print(f"炮孔操作模式: {self.hole_operation_mode}")
                if self.hole_operation_mode == "add":
                    # 添加炮孔模式
                    self.add_hole_at_position(scene_pos.x(), scene_pos.y())
                    return
                elif self.hole_operation_mode == "delete":
                    # 删除炮孔模式
                    self.delete_hole_at_position(scene_pos)
                    return
                elif self.hole_operation_mode == "edit":
                    # 编辑炮孔模式
                    self.edit_hole_at_position(scene_pos)
                    return
        
        # 处理右键点击
        elif event.button() == Qt.RightButton:
            # 处理绘制模式 (右键结束绘制)
            if hasattr(self, 'drawing_mode') and self.drawing_mode:
                print(f"右键结束绘制模式: {self.drawing_mode}")
                self.finish_drawing()
                return
            
            # 处理精细化设计模式 (右键退出)
            if hasattr(self, 'detailed_design_mode') and self.detailed_design_mode:
                print("右键退出精细化设计模式")
                self.parent().exit_detailed_design_mode()
                if hasattr(self.parent().parent(), 'update_status'):
                    self.parent().parent().update_status("已退出精细化设计模式")
                return
        
        print("调用父类 mousePressEvent")
        super().mousePressEvent(event)
    
    def add_hole_at_position(self, x, y):
        """在指定位置添加炮孔"""
        # 生成炮孔编号
        holes = self.get_blast_holes_from_view()
        hole_count = len(holes) + 1
        hole_id = f"H{hole_count:03d}"
        
        # 创建炮孔，使用标准大小20像素（对应110mm）
        hole = BlastHoleItem(x, y, 20)
        hole.hole_id = hole_id
        hole.diameter_value = 110.0  # 设置默认孔径为110mm
        hole.update_text_label()
        
        # 添加到场景
        self.scene().addItem(hole)
        
        # 保存状态
        self.design_area.save_state()
        
        # 发送信号通知主窗口
        main_window = self.design_area.get_main_window()
        if main_window:
            main_window.update_status(f"已添加炮孔: {hole_id}")
    
    def delete_hole_at_position(self, scene_pos):
        """删除指定位置的炮孔"""
        item = self.scene().itemAt(scene_pos, self.transform())
        if isinstance(item, BlastHoleItem):
            hole_id = item.hole_id
            print(f"正在删除炮孔: {hole_id}")
            
            # 先清除与此炮孔相关的连接线
            item.clear_related_connection_lines()
            
            # 由于文本标签设置了parentItem，删除炮孔时会自动删除关联的文本
            self.scene().removeItem(item)
            print(f"炮孔 {hole_id} 及其相关连接线已删除")
            
            main_window = self.design_area.get_main_window()
            if main_window:
                main_window.update_status(f"已删除炮孔: {hole_id}")
            # 保存删除后的状态
            self.design_area.save_state()
    
    def edit_hole_at_position(self, scene_pos):
        """编辑指定位置的炮孔"""
        item = self.scene().itemAt(scene_pos, self.transform())
        if isinstance(item, BlastHoleItem):
            self.edit_hole_item(item)
    
    def edit_hole_item(self, hole_item):
        """编辑炮孔项"""
        from ..dialogs.hole_design_dialogs import HoleEditDialog
        
        # 获取当前数据
        hole_data = hole_item.get_data()
        
        # 打开编辑对话框
        dialog = HoleEditDialog(hole_data, self)
        if dialog.exec_() == QDialog.Accepted:
            # 更新炮孔数据
            new_data = dialog.get_data()
            hole_item.set_data(new_data)
            
            # 保存状态
            self.design_area.save_state()
            
            if hasattr(self.parent(), 'update_status'):
                self.parent().update_status(f"已更新炮孔: {new_data['hole_id']}")
    
    def get_blast_holes_from_view(self):
        """从视图获取所有炮孔"""
        holes = []
        for item in self.scene().items():
            if isinstance(item, BlastHoleItem):
                holes.append(item)
        return holes
    
    def wheelEvent(self, event):
        """滚轮事件 - 实现缩放功能"""
        if event.modifiers() & Qt.ControlModifier:
            # 按住Ctrl键时进行缩放
            factor = 1.2
            if event.angleDelta().y() < 0:
                factor = 1.0 / factor
            
            self.scale(factor, factor)
            self.zoom_factor *= factor
        else:
            super().wheelEvent(event)
    
    def set_mode(self, mode):
        """设置操作模式"""
        self.current_mode = mode
        
        if mode == "move":
            self.setDragMode(QGraphicsView.ScrollHandDrag)
        elif mode == "zoom":
            self.setDragMode(QGraphicsView.RubberBandDrag)
        elif mode == "explosive_amount_edit":
            self.setDragMode(QGraphicsView.NoDrag)
            self.setCursor(Qt.PointingHandCursor)
        else:
            self.setDragMode(QGraphicsView.RubberBandDrag)
            self.setCursor(Qt.ArrowCursor)
    
    def set_hole_operation_mode(self, mode):
        """设置炮孔操作模式"""
        self.hole_operation_mode = mode
        
        # 设置鼠标光标
        if mode == "add":
            self.setCursor(Qt.CrossCursor)
        elif mode == "delete":
            self.setCursor(Qt.PointingHandCursor)
        elif mode == "edit":
            self.setCursor(Qt.PointingHandCursor)
        else:
            self.setCursor(Qt.ArrowCursor)
    
    def zoom_in(self):
        """放大"""
        factor = 1.2
        self.scale(factor, factor)
        self.zoom_factor *= factor
    
    def zoom_out(self):
        """缩小"""
        factor = 1.0 / 1.2
        self.scale(factor, factor)
        self.zoom_factor *= factor
    
    def fit_view(self):
        """适应全图"""
        self.fitInView(self.scene().itemsBoundingRect(), Qt.KeepAspectRatio)
        self.zoom_factor = 1.0
    
    def handle_special_hole_click(self, scene_pos, hole_type):
        """处理特殊孔类型设置点击"""
        item = self.scene().itemAt(scene_pos, self.transform())
        if isinstance(item, BlastHoleItem):
            item.special_hole_type = hole_type
            item.update_hole_color()
            
            # 同时更新炸药信息为当前配置
            main_window = self.parent().parent()
            if hasattr(main_window, 'current_explosive_type'):
                item.explosive_type = main_window.current_explosive_type
                item.charge_structure = getattr(main_window, 'current_charge_structure', '连续装药')
            
            # 更新炸药相关标签显示
            if hasattr(item, 'update_explosive_display'):
                item.update_explosive_display()
            
            if hasattr(self.parent(), 'update_status'):
                self.parent().update_status(f"已将炮孔 {item.hole_id} 设置为{hole_type}")
            
            # 保存状态
            self.design_area.save_state()
            
            # 自动退出设置模式，恢复为普通孔模式
            if hasattr(main_window, 'current_special_hole_type'):
                main_window.current_special_hole_type = "普通孔"
                self.parent().special_hole_mode = None
                self.setCursor(Qt.ArrowCursor)
                if hasattr(self.parent(), 'update_status'):
                    self.parent().update_status("特殊孔设置完成，已恢复为普通孔模式")
    
    def handle_drilling_equipment_click(self, scene_pos):
        """处理打孔设备设置点击"""
        item = self.scene().itemAt(scene_pos, self.transform())
        if isinstance(item, BlastHoleItem):
            main_window = self.parent().parent()
            if (hasattr(main_window, 'current_drilling_equipment') and 
                hasattr(main_window, 'current_equipment_diameter')):
                
                equipment = main_window.current_drilling_equipment
                diameter = main_window.current_equipment_diameter
                
                item.drilling_equipment = equipment
                item.equipment_diameter = diameter
                item.diameter_value = diameter  # 同时更新孔径值
                item.update_diameter(diameter)  # 更新视觉大小
                
                # 同时更新炸药信息为当前配置
                if hasattr(main_window, 'current_explosive_type'):
                    item.explosive_type = main_window.current_explosive_type
                    item.charge_structure = getattr(main_window, 'current_charge_structure', '连续装药')
                
                # 更新文本标签和炸药相关标签显示
                item.update_text_label()
                if hasattr(item, 'update_explosive_display'):
                    item.update_explosive_display()
                
                # 保存状态
                self.design_area.save_state()
                
                if hasattr(self.parent(), 'update_status'):
                    self.parent().update_status(f"已将炮孔 {item.hole_id} 设置为{equipment}(孔径{diameter}mm)")

    def handle_detailed_design_click(self, scene_pos):
        """处理精细化设计点击"""
        print(f"=== handle_detailed_design_click 被调用 ===")
        print(f"点击位置: ({scene_pos.x():.2f}, {scene_pos.y():.2f})")
        
        item = self.scene().itemAt(scene_pos, self.transform())
        print(f"点击的项目: {item}")
        print(f"是否为BlastHoleItem: {isinstance(item, BlastHoleItem)}")
        
        if isinstance(item, BlastHoleItem):
            print(f"点击的炮孔ID: {item.hole_id}")
            
            # 从父级DesignArea获取主窗口对象
            design_area = self.parent()
            main_window = None
            print(f"图形视图的父级对象: {design_area}")
            
            if design_area and hasattr(design_area, 'get_main_window'):
                print("通过DesignArea获取主窗口...")
                main_window = design_area.get_main_window()
            
            # 备用方案：使用原来的查找方法
            if not main_window:
                print("备用方案：使用_find_main_window查找...")
                main_window = self._find_main_window()
            
            print(f"找到的主窗口对象: {main_window}")
            print(f"精细化设计模式: {self.detailed_design_mode}")
            
            if self.detailed_design_mode == 'rock':
                print("进入岩石设置分支...")
                # 总是调用主窗口的岩石设置方法来统一处理
                if main_window and hasattr(main_window, 'set_rock_type_for_selected_hole'):
                    print(f"找到主窗口的岩石设置方法，处理炮孔: {item.hole_id}")
                    # 先选中炮孔，然后调用主窗口方法
                    print("清除场景选择...")
                    self.scene().clearSelection()
                    print("选中当前炮孔...")
                    item.setSelected(True)
                    print(f"调用主窗口方法: set_rock_type_for_selected_hole({item})")
                    main_window.set_rock_type_for_selected_hole(item)
                    print("主窗口岩石设置方法调用完成")
                else:
                    print("ERROR: 主窗口未找到或方法不存在，无法处理岩石设置")
                    self.parent().exit_detailed_design_mode()
                    if hasattr(self.parent().parent(), 'update_status'):
                        self.parent().parent().update_status("岩石设置失败：无法找到主窗口")
            elif self.detailed_design_mode == 'diameter':
                print("进入孔径设置分支...")
                self.set_hole_diameter(item)
            elif self.detailed_design_mode == 'explosive':
                print("进入炸药设置分支...")
                # 调用主窗口的炸药设置方法来统一处理
                if main_window and hasattr(main_window, 'set_explosive_type_for_selected_hole'):
                    print(f"调用主窗口的炸药设置方法，处理炮孔: {item.hole_id}")
                    # 先选中炮孔，然后调用主窗口方法
                    self.scene().clearSelection()
                    item.setSelected(True)
                    main_window.set_explosive_type_for_selected_hole(item)
                else:
                    print("主窗口未找到或方法不存在，使用备用方法")
                    self.set_hole_explosive_type(item)
            elif self.detailed_design_mode == 'spacing':
                print("进入间排距设置分支...")
                self.set_hole_spacing(item)
            else:
                print(f"未知的精细化设计模式: {self.detailed_design_mode}")
        else:
            print("点击的不是炮孔项目")
        
        print("=== handle_detailed_design_click 处理完成 ===\n")
    
    def _find_main_window(self):
        """查找主窗口对象"""
        # 从当前对象开始，向上查找MainWindow对象
        widget = self
        while widget:
            parent = widget.parent()
            if parent and hasattr(parent, '__class__'):
                # 检查类名是否为MainWindow
                if parent.__class__.__name__ == 'MainWindow':
                    return parent
                # 也可以检查是否有MainWindow的特有方法
                elif hasattr(parent, 'set_rock_type_for_selected_hole') and hasattr(parent, 'current_rock_type'):
                    return parent
            widget = parent
        
        # 如果通过parent()无法找到，尝试从场景的视图中查找
        if hasattr(self, 'scene') and self.scene():
            for view in self.scene().views():
                current_widget = view
                while current_widget:
                    if hasattr(current_widget, '__class__') and current_widget.__class__.__name__ == 'MainWindow':
                        return current_widget
                    elif hasattr(current_widget, 'set_rock_type_for_selected_hole') and hasattr(current_widget, 'current_rock_type'):
                        return current_widget
                    current_widget = current_widget.parent()
        
        # 最后尝试从父级对象的parent()中查找
        parent_widget = self.parent()
        if parent_widget:
            grandparent = parent_widget.parent()
            if grandparent and hasattr(grandparent, '__class__'):
                if grandparent.__class__.__name__ == 'MainWindow':
                    return grandparent
                elif hasattr(grandparent, 'set_rock_type_for_selected_hole') and hasattr(grandparent, 'current_rock_type'):
                    return grandparent
        
        return None
    
    def set_hole_diameter(self, hole_item):
        """设置炮孔孔径"""
        settings = self.detailed_design_settings
        new_diameter = settings.get('diameter', 110.0)
        hole_item.diameter_value = new_diameter
        if settings.get('equipment'):
            hole_item.drilling_equipment = settings['equipment']
            hole_item.equipment_diameter = new_diameter
        
        # 更新视觉大小和标签
        hole_item.update_diameter(new_diameter)
        hole_item.update_text_label()
        print(f"设置炮孔 {hole_item.hole_id} 孔径为 {new_diameter}mm")
        
        if hasattr(self.parent().parent(), 'update_status'):
            self.parent().parent().update_status(f"已设置炮孔 {hole_item.hole_id} 孔径为 {new_diameter}mm")
    
    def set_hole_explosive_type(self, hole_item):
        """设置炮孔炸药类型和装药结构"""
        settings = self.detailed_design_settings
        hole_item.explosive_type = settings.get('explosive_type', '2号岩石铵梯炸药')
        hole_item.charge_structure = settings.get('charge_structure', '连续装药')
        
        # 更新所有相关显示
        hole_item.update_text_label()
        hole_item.update_explosive_display()
        
        print(f"设置炮孔 {hole_item.hole_id} 炸药类型: {hole_item.explosive_type}, 装药结构: {hole_item.charge_structure}")
        
        if hasattr(self.parent().parent(), 'update_status'):
            self.parent().parent().update_status(f"已设置炮孔 {hole_item.hole_id} 炸药: {hole_item.explosive_type} ({hole_item.charge_structure})")
    
    def set_hole_spacing(self, hole_item):
        """设置炮孔间排距"""
        settings = self.detailed_design_settings
        hole_item.spacing = settings.get('spacing', 3.5)
        hole_item.row_spacing = settings.get('row_spacing', 4.0)
        hole_item.update_text_label()
        print(f"设置炮孔 {hole_item.hole_id} 间排距为: {hole_item.spacing}m × {hole_item.row_spacing}m")
    
    def handle_drawing_click(self, scene_pos):
        """处理绘制模式的鼠标点击"""
        x, y = scene_pos.x(), scene_pos.y()
        
        if self.drawing_mode == "work_surface":
            # 作业面绘制：添加点到路径
            self.drawing_points.append((x, y))
            print(f"作业面添加点: ({x:.2f}, {y:.2f}), 总点数: {len(self.drawing_points)}")
            
            # 在场景中添加临时点标记
            temp_point = self.scene().addEllipse(x-2, y-2, 4, 4, QPen(Qt.red), QBrush(Qt.red))
            temp_point.setZValue(1000)  # 确保在最上层
            
        elif self.drawing_mode == "boundary":
            # 边界线绘制：添加点到路径
            self.drawing_points.append((x, y))
            print(f"边界线添加点: ({x:.2f}, {y:.2f}), 总点数: {len(self.drawing_points)}")
            
            # 在场景中添加临时点标记
            temp_point = self.scene().addEllipse(x-2, y-2, 4, 4, QPen(Qt.blue), QBrush(Qt.blue))
            temp_point.setZValue(1000)
            
        elif self.drawing_mode == "text":
            # 文本添加：直接在点击位置添加文本
            self.add_text_at_position(x, y)
            
        elif self.drawing_mode == "reference_point":
            # 参考点添加：直接在点击位置添加参考点
            self.add_reference_point_at_position(x, y)
    
    def finish_drawing(self):
        """完成绘制"""
        if self.drawing_mode == "work_surface":
            if len(self.drawing_points) >= 2:
                # 创建作业面
                points = [QPointF(x, y) for x, y in self.drawing_points]
                work_surface = WorkSurfaceItem(points)
                work_surface.item_type = "work_surface"
                self.scene().addItem(work_surface)
                print(f"作业面创建完成，包含 {len(points)} 个点")
                
                # 清除临时点标记
                self.clear_temp_markers()
                
                # 保存状态
                self.design_area.save_state()
                
                # 通知主窗口
                main_window = self.design_area.get_main_window()
                if main_window:
                    main_window.update_status(f"作业面绘制完成，包含 {len(points)} 个点")
            else:
                print("作业面至少需要2个点")
                main_window = self.design_area.get_main_window()
                if main_window:
                    main_window.update_status("作业面至少需要2个点，绘制取消")
        
        elif self.drawing_mode == "boundary":
            if len(self.drawing_points) >= 3:
                # 创建闭合边界线
                points = [QPointF(x, y) for x, y in self.drawing_points]
                boundary = BoundaryPolygonItem(points)
                boundary.item_type = "boundary"
                self.scene().addItem(boundary)
                print(f"边界线创建完成，包含 {len(points)} 个点")
                
                # 清除临时点标记
                self.clear_temp_markers()
                
                # 保存状态
                self.design_area.save_state()
                
                # 通知主窗口
                main_window = self.design_area.get_main_window()
                if main_window:
                    main_window.update_status(f"边界线绘制完成，包含 {len(points)} 个点")
            else:
                print("边界线至少需要3个点")
                main_window = self.design_area.get_main_window()
                if main_window:
                    main_window.update_status("边界线至少需要3个点，绘制取消")
        
        # 退出绘制模式
        self.exit_drawing_mode()
    
    def clear_temp_markers(self):
        """清除临时点标记"""
        items_to_remove = []
        for item in self.scene().items():
            # 查找临时标记点（小圆点且Z值为1000）
            if (isinstance(item, QGraphicsEllipseItem) and 
                item.rect().width() == 4 and 
                item.zValue() == 1000):
                items_to_remove.append(item)
        
        for item in items_to_remove:
            self.scene().removeItem(item)
        print(f"清除了 {len(items_to_remove)} 个临时标记点")
    
    def add_text_at_position(self, x, y):
        """在指定位置添加文本"""
        from PyQt5.QtWidgets import QInputDialog
        
        text, ok = QInputDialog.getText(self, "添加文本", "请输入文本内容:")
        if ok and text:
            # 创建文本项
            text_item = QGraphicsTextItem(text)
            text_item.setPos(x, y)
            text_item.setFlag(QGraphicsItem.ItemIsMovable, True)
            text_item.setFlag(QGraphicsItem.ItemIsSelectable, True)
            text_item.item_type = "text_annotation"
            
            # 设置文本样式
            font = QFont("SimHei", 10)
            text_item.setFont(font)
            text_item.setDefaultTextColor(Qt.black)
            
            self.scene().addItem(text_item)
            print(f"文本添加完成: '{text}' 位置({x:.2f}, {y:.2f})")
            
            # 保存状态
            self.design_area.save_state()
            
            # 退出文本添加模式
            self.exit_drawing_mode()
            
            # 通知主窗口
            main_window = self.design_area.get_main_window()
            if main_window:
                main_window.update_status(f"文本添加完成: '{text}'")
    
    def add_reference_point_at_position(self, x, y):
        """在指定位置添加参考点"""
        # 创建参考点（小圆圈）
        ref_point = QGraphicsEllipseItem(x-3, y-3, 6, 6)
        ref_point.setBrush(QBrush(Qt.red))
        ref_point.setPen(QPen(Qt.darkRed, 2))
        ref_point.setFlag(QGraphicsItem.ItemIsMovable, True)
        ref_point.setFlag(QGraphicsItem.ItemIsSelectable, True)
        ref_point.item_type = "reference_point"
        
        self.scene().addItem(ref_point)
        print(f"参考点添加完成，位置({x:.2f}, {y:.2f})")
        
        # 保存状态
        self.design_area.save_state()
        
        # 退出参考点添加模式
        self.exit_drawing_mode()
        
        # 通知主窗口
        main_window = self.design_area.get_main_window()
        if main_window:
            main_window.update_status(f"参考点添加完成，位置({x:.2f}, {y:.2f})")
    
    def exit_drawing_mode(self):
        """退出绘制模式"""
        self.drawing_mode = None
        self.drawing_points = []
        self.setCursor(Qt.ArrowCursor)
        print("退出绘制模式")

class BlastHoleItem(QGraphicsEllipseItem):
    """炮孔图形项"""
    
    def __init__(self, x, y, diameter=6, parent=None):
        super().__init__(-diameter/2, -diameter/2, diameter, diameter, parent)
        self.setPos(x, y)
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        
        # 设置样式
        self.setPen(QPen(Qt.black, 1))
        self.setBrush(QBrush(Qt.yellow))
        
        # 炮孔属性 - 基本信息
        self.hole_id = ""
        self.hole_type = "主炮孔"
        self.depth = 15.0
        # 如果传入的diameter是像素值（<=40），则设置默认毫米值
        if diameter <= 40:
            self.diameter_value = 110.0  # 默认110mm
        else:
            self.diameter_value = diameter  # 直接使用传入的毫米值
        self.rock_type = "花岗岩"
        self.explosive_type = "2号岩石铵梯炸药"
        self.explosive_amount = 25.0
        self.charge_structure = "连续装药"  # 装药结构：连续装药、分段装药
        self.layout_mode = "矩形"  # 布孔模式：矩形或三角形
        
        # 精细化设计属性
        self.special_hole_type = "普通孔"  # 起爆孔类型：起始孔、死孔、失火孔、普通孔
        self.drilling_equipment = "默认设备"  # 打孔设备
        self.equipment_diameter = 110.0  # 设备对应孔径
        
        # 坐标信息
        self.x_coord = x
        self.y_coord = y
        self.z_coord = 100.0
        self.inclination = 0.0
        self.azimuth = 0.0
        
        # AVV参数
        self.spacing = 3.5
        self.row_spacing = 4.0
        self.bench_height = 15.0
        self.over_drill = 1.0
        self.stemming_length = 3.0
        self.charge_length = 12.0
        
        # 炸药量编辑相关属性
        self.backfill_length = 3.0  # 回填长度
        self.project_type = "露天矿山剥离爆破"  # 工程类型
        self.unit_charge = 0.0  # 单位长度装药量
        
        # 添加文本标签（显示在炮孔右侧）
        self.text_item = QGraphicsTextItem(self.hole_id, self)
        self.text_item.setPos(10, -10)
        self.text_item.setFont(QFont("Arial", 8))
        
        # 添加孔径标签（显示在炮孔右侧）
        self.diameter_text = QGraphicsTextItem("", self)
        self.diameter_text.setPos(25, 25)
        self.diameter_text.setFont(QFont("Arial", 7))
        self.diameter_text.setDefaultTextColor(Qt.blue)
        self.diameter_text.setVisible(False)
        
        # 添加岩石类型标签（显示在炮孔右侧）
        self.rock_text = QGraphicsTextItem("", self)
        self.rock_text.setPos(25, 40)
        self.rock_text.setFont(QFont("Arial", 6))
        self.rock_text.setDefaultTextColor(Qt.darkGreen)
        self.rock_text.setVisible(False)
        
        # 添加炸药信息标签（显示在炮孔右侧）
        self.explosive_text = QGraphicsTextItem("", self)
        self.explosive_text.setPos(25, 70)
        self.explosive_text.setFont(QFont("Arial", 6))
        self.explosive_text.setDefaultTextColor(Qt.darkMagenta)
        self.explosive_text.setVisible(False)
        
        # 添加间排距标签
        self.spacing_text = QGraphicsTextItem("", self)
        self.spacing_text.setPos(-20, 25)
        self.spacing_text.setFont(QFont("Arial", 7))
        self.spacing_text.setDefaultTextColor(Qt.darkRed)
        self.spacing_text.setVisible(False)
        
        # 添加炸药量标签
        self.explosive_amount_text = QGraphicsTextItem("", self)
        self.explosive_amount_text.setPos(-20, 40)
        self.explosive_amount_text.setFont(QFont("Arial", 7))
        self.explosive_amount_text.setDefaultTextColor(Qt.darkBlue)
        self.explosive_amount_text.setVisible(False)
        
        # 确保文本标签跟随炮孔
        # 确保文本标签跟随炮孔
        self.text_item.setParentItem(self)
        self.diameter_text.setParentItem(self)
        self.rock_text.setParentItem(self)
        self.explosive_text.setParentItem(self)
        self.spacing_text.setParentItem(self)
        self.explosive_amount_text.setParentItem(self)
        
        # 初始化时更新所有标签
        self.update_text_label()
        self.update_rock_display()
        self.update_explosive_display()
    
    def update_position(self, x, y):
        """更新位置"""
        self.x_coord = x
        self.y_coord = y
        self.setPos(x, y)
    
    def update_text_label(self):
        """更新文本标签"""
        self.update_label_visibility_and_content()
    
    def update_diameter(self, new_diameter):
        """更新孔径和视觉大小"""
        self.diameter_value = new_diameter
        # 计算视觉显示的大小（将毫米转换为合适的像素大小）
        # 110mm孔径对应12像素，其他按比例缩放
        visual_size = max(5, min(30, new_diameter * 12 / 110))
        self.setRect(-visual_size/2, -visual_size/2, visual_size, visual_size)
    
    def set_extra_labels_visible(self, visible):
        """设置除ID外的所有附加标签的可见性"""
        self.diameter_text.setVisible(visible)
        self.rock_text.setVisible(visible)
        self.explosive_text.setVisible(visible)
        self.spacing_text.setVisible(visible)
        self.explosive_amount_text.setVisible(visible)
        
    def update_label_visibility_and_content(self):
        """统一更新所有标签的可见性和内容"""
        design_area = self.get_design_area()
        
        # 默认只显示炮孔编号，其他标签隐藏
        is_rock_visible = False
        is_explosive_visible = False
        is_diameter_visible = False
        is_spacing_visible = False
        is_explosive_amount_visible = False

        # 只有在精细化设计模式下才显示对应的标签
        if design_area:
            if getattr(design_area, 'is_diameter_setting_mode', False):
                is_diameter_visible = True
            elif getattr(design_area, 'is_rock_setting_mode', False):
                is_rock_visible = True
            elif getattr(design_area, 'is_explosive_setting_mode', False):
                is_explosive_visible = True
            elif getattr(design_area, 'is_spacing_setting_mode', False):
                is_spacing_visible = True
            elif getattr(design_area, 'is_explosive_amount_setting_mode', False):
                is_explosive_amount_visible = True

        self.rock_text.setVisible(is_rock_visible)
        self.explosive_text.setVisible(is_explosive_visible)
        self.diameter_text.setVisible(is_diameter_visible)
        self.spacing_text.setVisible(is_spacing_visible)
        self.explosive_amount_text.setVisible(is_explosive_amount_visible)

        # 更新可见标签的内容
        self.text_item.setPlainText(self.hole_id)
        
        if is_diameter_visible:
            diameter_info = f"φ{self.diameter_value:.0f}mm"
            if hasattr(self, 'drilling_equipment') and self.drilling_equipment != "默认设备":
                diameter_info = f"{self.drilling_equipment}\nφ{self.equipment_diameter:.0f}mm"
            self.diameter_text.setPlainText(diameter_info)
            self.diameter_text.setPos(25, 25)

        if is_rock_visible:
            self.rock_text.setPlainText(self.rock_type)
            color_map = {'花岗岩': Qt.darkRed, '石灰岩': Qt.darkBlue, '砂岩': Qt.darkYellow, '页岩': Qt.darkGray, '玄武岩': Qt.black, '大理岩': Qt.darkCyan, '片麻岩': Qt.darkMagenta, '辉绿岩': Qt.darkGreen}
            self.rock_text.setDefaultTextColor(color_map.get(self.rock_type, Qt.darkGreen))
            rock_y_pos = 55 if '\n' in self.diameter_text.toPlainText() else 45
            self.rock_text.setPos(25, rock_y_pos)

        if is_explosive_visible:
            explosive_short = self.explosive_type.replace('号岩石铵梯炸药', '号铵梯').replace('炸药', '')
            structure_short = self.charge_structure.replace('装药', '')
            self.explosive_text.setPlainText(f"{explosive_short}\n{structure_short}")
            self.explosive_text.setDefaultTextColor(Qt.darkMagenta if self.charge_structure == "连续装药" else Qt.darkCyan)
            rock_y_pos = self.rock_text.pos().y()
            self.explosive_text.setPos(25, rock_y_pos + 25)

        if is_spacing_visible:
            spacing_info = f"A: {self.spacing:.1f}m\nV: {self.row_spacing:.1f}m"
            self.spacing_text.setPlainText(spacing_info)

        if is_explosive_amount_visible:
            self.explosive_amount_text.setPlainText(f"{self.explosive_amount:.2f} kg")
            # 在炸药量模式下，将此标签移动到主ID下方
            self.explosive_amount_text.setPos(10, 10)
        else:
            # 恢复默认位置
            self.explosive_amount_text.setPos(25, 55)

        if self.scene():
            expanded_rect = self.mapRectToScene(self.boundingRect()).adjusted(-100, -50, 100, 150)
            self.scene().update(expanded_rect)
    
    def update_hole_color(self):
        """根据孔类型更新颜色"""
        from PyQt5.QtGui import QColor, QBrush
        
        # 优先根据智能布孔模式设置颜色
        if hasattr(self, 'layout_mode') and self.layout_mode == '三角形布孔':
            self.setBrush(QBrush(QColor("#FF00FF"))) # Magenta
            return
        elif hasattr(self, 'layout_mode') and self.layout_mode == '矩形布孔':
            self.setBrush(QBrush(Qt.yellow))
            return

        # 其次按特殊孔类型（起爆孔）设置颜色
        if hasattr(self, 'special_hole_type') and self.special_hole_type != "普通孔":
            if self.special_hole_type == "起始孔":
                self.setBrush(QBrush(Qt.red))
                return
            elif self.special_hole_type == "死孔":
                self.setBrush(QBrush(QColor(192, 192, 192)))  # 亮灰色
                return
            elif self.special_hole_type == "失火孔":
                self.setBrush(QBrush(Qt.yellow))
                return
            elif self.special_hole_type == "普通孔":
                self.setBrush(QBrush(Qt.blue))
                return
        
        # 普通炮孔按孔类型设置颜色
        if self.hole_type == "主炮孔":
            self.setBrush(QBrush(Qt.yellow))
        elif self.hole_type == "辅助孔":
            self.setBrush(QBrush(Qt.cyan))
        elif self.hole_type == "缓冲孔":
            self.setBrush(QBrush(Qt.green))
        elif self.hole_type == "周边孔":
            self.setBrush(QBrush(Qt.blue))
        elif self.hole_type == "保护孔":
            self.setBrush(QBrush(Qt.red))
        else:
            self.setBrush(QBrush(Qt.yellow))  # 默认颜色
    
    def update_rock_display(self):
        """更新岩石信息显示"""
        pass
    
    def update_explosive_display(self):
        """更新炸药信息显示"""
        pass
    
    def get_parameter_summary(self):
        """获取炮孔参数摘要"""
        return f"""炮孔参数信息:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 基本信息:
   • 炮孔编号: {self.hole_id}
   • 孔类型: {self.hole_type}
   • 孔径: {self.diameter_value:.1f}mm
   • 孔深: {self.depth:.1f}m
   • 岩石类型: {self.rock_type}
   • 炸药类型: {self.explosive_type}
   • 炸药量: {self.explosive_amount:.2f}kg

🔧 精细化设计:
   • 起爆孔类型: {getattr(self, 'special_hole_type', '普通孔')}
   • 打孔设备: {getattr(self, 'drilling_equipment', '默认设备')}
   • 设备孔径: {getattr(self, 'equipment_diameter', 110.0):.1f}mm

📍 坐标信息:
   • X坐标: {self.x_coord:.2f}
   • Y坐标: {self.y_coord:.2f}
   • Z坐标: {self.z_coord:.2f}
   • 倾角: {self.inclination:.1f}°
   • 方位角: {self.azimuth:.1f}°

⚡ AVV参数:
   • 间距(A): {self.spacing:.1f}m
   • 排距(V): {self.row_spacing:.1f}m
   • 段高(V): {self.bench_height:.1f}m
   • 超钻: {self.over_drill:.1f}m
   • 填塞长度: {self.stemming_length:.1f}m
   • 装药长度: {self.charge_length:.1f}m

🎨 显示信息:
   • 布孔模式: {getattr(self, 'layout_mode', '矩形')}
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"""
    
    def get_data(self):
        """获取炮孔数据"""
        return {
            'hole_id': self.hole_id,
            'hole_type': self.hole_type,
            'depth': self.depth,
            'diameter': self.diameter_value,
            'rock_type': self.rock_type,
            'explosive_type': self.explosive_type,
            'explosive_amount': self.explosive_amount,
            'charge_structure': getattr(self, 'charge_structure', '连续装药'),
            'layout_mode': getattr(self, 'layout_mode', '矩形'),
            'special_hole_type': getattr(self, 'special_hole_type', '普通孔'),
            'drilling_equipment': getattr(self, 'drilling_equipment', '默认设备'),
            'equipment_diameter': getattr(self, 'equipment_diameter', 110.0),
            'x': self.x_coord,
            'y': self.y_coord,
            'z': self.z_coord,
            'inclination': self.inclination,
            'azimuth': self.azimuth,
            'spacing': self.spacing,
            'row_spacing': self.row_spacing,
            'bench_height': self.bench_height,
            'over_drill': self.over_drill,
            'stemming_length': self.stemming_length,
            'charge_length': self.charge_length
        }
    
    def set_data(self, data):
        """设置炮孔数据"""
        self.hole_id = data.get('hole_id', '')
        self.hole_type = data.get('hole_type', '主炮孔')
        self.depth = data.get('depth', 15.0)
        self.diameter_value = data.get('diameter', 110.0)
        self.rock_type = data.get('rock_type', '花岗岩')
        self.explosive_type = data.get('explosive_type', '2号岩石铵梯炸药')
        self.explosive_amount = data.get('explosive_amount', 25.0)
        self.charge_structure = data.get('charge_structure', '连续装药')
        self.layout_mode = data.get('layout_mode', '矩形')
        self.special_hole_type = data.get('special_hole_type', '普通孔')
        self.drilling_equipment = data.get('drilling_equipment', '默认设备')
        self.equipment_diameter = data.get('equipment_diameter', 110.0)
        self.x_coord = data.get('x', 0.0)
        self.y_coord = data.get('y', 0.0)
        self.z_coord = data.get('z', 100.0)
        self.inclination = data.get('inclination', 0.0)
        self.azimuth = data.get('azimuth', 0.0)
        self.spacing = data.get('spacing', 3.5)
        self.row_spacing = data.get('row_spacing', 4.0)
        self.bench_height = data.get('bench_height', 15.0)
        self.over_drill = data.get('over_drill', 1.0)
        self.stemming_length = data.get('stemming_length', 3.0)
        self.charge_length = data.get('charge_length', 12.0)
        
        # 更新位置、孔径大小和标签
        self.setPos(self.x_coord, self.y_coord)
        self.update_diameter(self.diameter_value)
        self.update_text_label()
        self.update_hole_color()  # 确保颜色在恢复时正确应用

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 移动完成时保存状态并清除相关连接线"""
        super().mouseReleaseEvent(event)
        
        # 如果位置发生了变化，清除相关连接线并保存状态
        if hasattr(self, '_last_pos'):
            current_pos = self.pos()
            if (abs(current_pos.x() - self._last_pos.x()) > 1 or 
                abs(current_pos.y() - self._last_pos.y()) > 1):
                print(f"炮孔 {self.hole_id} 位置发生变化，从 ({self._last_pos.x():.1f}, {self._last_pos.y():.1f}) 移动到 ({current_pos.x():.1f}, {current_pos.y():.1f})")
                
                # 清除与此炮孔相关的连接线
                self.clear_related_connection_lines()
                
                # 位置有明显变化，保存状态
                design_area = self.get_design_area()
                if design_area:
                    design_area.save_state()
                    print(f"炮孔 {self.hole_id} 移动后状态已保存")
            del self._last_pos
    
    def mousePressEvent(self, event):
        """鼠标按下事件 - 记录初始位置"""
        super().mousePressEvent(event)
        self._last_pos = self.pos()
    
    def get_design_area(self):
        """获取设计区域对象"""
        scene = self.scene()
        if scene:
            for view in scene.views():
                parent = view.parent()
                if hasattr(parent, 'save_state'):
                    return parent
        return None
    
    def clear_related_connection_lines(self):
        """清除与此炮孔相关的连接线"""
        if not self.scene():
            return
        
        hole_id = self.hole_id
        print(f"开始清除炮孔 {hole_id} 相关的连接线...")
        
        # 收集需要删除的连接线
        lines_to_remove = []
        removed_count = 0
        
        for item in self.scene().items():
            # 检查是否是连接线并且与当前炮孔相关
            if (hasattr(item, 'item_type') and item.item_type == "connection_line" and
                hasattr(item, 'hole1_id') and hasattr(item, 'hole2_id')):
                
                # 如果连接线的任一端点是当前炮孔，则标记为需要删除
                if item.hole1_id == hole_id or item.hole2_id == hole_id:
                    lines_to_remove.append(item)
                    print(f"  找到需要删除的连接线: {item.hole1_id} -> {item.hole2_id}")
                    removed_count += 1
        
        # 删除收集到的连接线
        for line in lines_to_remove:
            print(f"  删除连接线: {line.hole1_id} -> {line.hole2_id}")
            self.scene().removeItem(line)
        
        if removed_count > 0:
            print(f"成功清除炮孔 {hole_id} 相关的 {removed_count} 条连接线")
        else:
            print(f"炮孔 {hole_id} 没有相关的连接线需要清除")

class BoundaryLineItem(QGraphicsLineItem):
    """边界线图形项"""
    
    def __init__(self, x1, y1, x2, y2, parent=None):
        super().__init__(x1, y1, x2, y2, parent)
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        
        # 设置样式
        self.setPen(QPen(Qt.red, 3, Qt.DashLine))

class WorkSurfaceItem(QGraphicsItem):
    """作业面图形项"""
    
    def __init__(self, points, parent=None):
        super().__init__(parent)
        self.points = points
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
    
    def boundingRect(self):
        """返回边界矩形"""
        if not self.points:
            return QRectF()
        
        min_x = min(p.x() for p in self.points)
        max_x = max(p.x() for p in self.points)
        min_y = min(p.y() for p in self.points)
        max_y = max(p.y() for p in self.points)
        
        return QRectF(min_x, min_y, max_x - min_x, max_y - min_y)
    
    def paint(self, painter, option, widget):
        """绘制作业面"""
        if len(self.points) < 2:
            return
        
        pen = QPen(Qt.red, 2, Qt.SolidLine)
        painter.setPen(pen)
        
        for i in range(len(self.points) - 1):
            painter.drawLine(self.points[i], self.points[i + 1])


class BoundaryPolygonItem(QGraphicsItem):
    """边界线多边形图形项"""
    
    def __init__(self, points, parent=None):
        super().__init__(parent)
        self.points = points
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
    
    def boundingRect(self):
        """返回边界矩形"""
        if not self.points:
            return QRectF()
        
        min_x = min(p.x() for p in self.points)
        max_x = max(p.x() for p in self.points)
        min_y = min(p.y() for p in self.points)
        max_y = max(p.y() for p in self.points)
        
        return QRectF(min_x, min_y, max_x - min_x, max_y - min_y)
    
    def paint(self, painter, option, widget):
        """绘制边界线"""
        if len(self.points) < 3:
            return
        
        painter.setPen(QPen(Qt.blue, 2, Qt.DashLine))
        
        # 绘制边界线段
        for i in range(len(self.points)):
            next_i = (i + 1) % len(self.points)  # 最后一个点连接到第一个点形成闭合
            painter.drawLine(self.points[i], self.points[next_i])
        
        # 在各个顶点绘制小圆点
        painter.setPen(QPen(Qt.darkBlue, 1))
        painter.setBrush(QBrush(Qt.blue))
        for point in self.points:
            painter.drawEllipse(point, 3, 3)


class ConnectionLineItem(QGraphicsLineItem):
    """连接线图形项"""
    
    def __init__(self, x1, y1, x2, y2, parent=None):
        super().__init__(x1, y1, x2, y2, parent)
        self.setFlag(QGraphicsItem.ItemIsMovable, True)
        self.setFlag(QGraphicsItem.ItemIsSelectable, True)
        
        # 设置样式
        self.setPen(QPen(Qt.blue, 2, Qt.DashLine))
        
        # 标记为连接线类型
        self.item_type = "connection_line"
        
        # 连接线的炮孔ID信息（如果有的话）
        self.hole1_id = None
        self.hole2_id = None
        self.connection_type = "manual"  # manual: 手动连接, auto: 自动连接
    
    def contextMenuEvent(self, event):
        """右键菜单事件"""
        from PyQt5.QtWidgets import QMenu, QAction
        
        # 创建右键菜单
        menu = QMenu()
        
        # 添加删除动作
        delete_action = QAction("删除连接线", menu)
        delete_action.triggered.connect(self.delete_connection_line)
        menu.addAction(delete_action)
        
        # 如果有炮孔连接信息，显示详细信息
        if self.hole1_id and self.hole2_id:
            menu.addSeparator()
            info_action = QAction(f"连接信息: {self.hole1_id} ↔ {self.hole2_id}", menu)
            info_action.setEnabled(False)  # 只显示信息，不可点击
            menu.addAction(info_action)
            
            type_action = QAction(f"连接类型: {self.connection_type}", menu)
            type_action.setEnabled(False)
            menu.addAction(type_action)
        
        # 显示菜单
        menu.exec_(event.screenPos())
    
    def delete_connection_line(self):
        """删除连接线"""
        from PyQt5.QtWidgets import QMessageBox
        
        # 确认删除
        if self.hole1_id and self.hole2_id:
            msg = f"确定删除连接线: {self.hole1_id} ↔ {self.hole2_id} 吗？"
        else:
            msg = "确定删除这条连接线吗？"
        
        reply = QMessageBox.question(None, "确认删除", msg,
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            # 从场景中删除
            if self.scene():
                self.scene().removeItem(self)
                print(f"已删除连接线: {self.hole1_id or '未知'} -> {self.hole2_id or '未知'}")
                
                # 通知设计区域保存状态
                design_area = self.get_design_area()
                if design_area:
                    design_area.save_state()
                    if hasattr(design_area, 'parent') and hasattr(design_area.parent(), 'update_status'):
                        design_area.parent().update_status(f"已删除连接线")
    
    def get_design_area(self):
        """获取设计区域对象"""
        scene = self.scene()
        if scene:
            for view in scene.views():
                parent = view.parent()
                if hasattr(parent, 'save_state'):
                    return parent
        return None
    
    def set_hole_connection(self, hole1_id, hole2_id, connection_type="manual"):
        """设置炮孔连接信息"""
        self.hole1_id = hole1_id
        self.hole2_id = hole2_id
        self.connection_type = connection_type

class DesignArea(QWidget):
    """设计区域主组件"""
    
    coordinates_changed = pyqtSignal(float, float)  # 坐标变化信号
    zoom_changed = pyqtSignal(float)  # 缩放变化信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        
        # 操作历史
        self.history = []
        self.history_index = -1
        self.max_history = 50  # 最大历史记录数
        
        # 智能布孔相关
        self.layout_mode = None
        self.layout_data = None
        self.layout_points = []
        
        # 精细化设计相关
        self.special_hole_mode = None
        self.drilling_mode = False
        
        # 精细化设计模式
        self.detailed_design_mode = None  # 'rock', 'explosive', 'spacing', etc.
        self.detailed_design_settings = {}
        
        # 炸药量编辑模式
        self.explosive_amount_edit_mode = False
        
        # 孔类型设置模式
        self.is_hole_type_mode = False
        
        # 孔径设置模式
        self.is_diameter_setting_mode = False

        # 岩石设置模式
        self.is_rock_setting_mode = False
        
        # 炸药设置模式
        self.is_explosive_setting_mode = False
        
        # 间排距设置模式
        self.is_spacing_setting_mode = False

        # 炸药量编辑模式
        self.is_explosive_amount_setting_mode = False

        # 默认视图模式（仅显示ID）
        self.is_default_view_mode = True 

        # 主窗口引用
        self.main_window = None
        
        # 连接场景选择变化信号
        self.graphics_scene.selectionChanged.connect(self.on_selection_changed)
        
        # 保存初始状态
        QTimer.singleShot(100, self.save_initial_state)
    
    def init_ui(self):
        """初始化界面"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 左侧主要内容区域
        left_layout = QVBoxLayout()
        left_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建堆叠组件，支持图形和表格切换
        self.stacked_widget = QStackedWidget()
        left_layout.addWidget(self.stacked_widget)
        
        # 创建图形视图
        self.setup_graphics_view()
        
        # 创建表格视图
        self.setup_table_view()
        
        # 默认显示图形视图
        self.stacked_widget.setCurrentIndex(0)
        
        # 将左侧布局添加到主布局
        left_widget = QWidget()
        left_widget.setLayout(left_layout)
        main_layout.addWidget(left_widget, 3)  # 占3份
        
        # 右侧参数显示面板
        self.setup_parameter_panel()
        main_layout.addWidget(self.parameter_panel, 1)  # 占1份
    
    def setup_graphics_view(self):
        """设置图形视图"""
        self.graphics_view = DesignGraphicsView(self)
        self.graphics_scene = QGraphicsScene()
        self.graphics_view.setScene(self.graphics_scene)
        
        # 设置场景范围
        self.graphics_scene.setSceneRect(-1000, -1000, 2000, 2000)
        
        # 连接信号
        self.graphics_view.mouse_moved.connect(self.on_mouse_moved)
        
        # 添加到堆叠组件
        self.stacked_widget.addWidget(self.graphics_view)
        
        # 添加网格背景
        self.add_grid_background()
    
    def setup_table_view(self):
        """设置表格视图"""
        self.table_widget = QTableWidget()
        self.table_widget.setColumnCount(9)
        self.table_widget.setHorizontalHeaderLabels([
            "孔号", "X坐标", "Y坐标", "孔深", "孔径", "岩石类型", "炸药类型", "装药结构", "装药量"
        ])
        
        # 设置表格样式
        header = self.table_widget.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        
        # 添加到堆叠组件
        self.stacked_widget.addWidget(self.table_widget)
    
    def setup_parameter_panel(self):
        """设置参数显示面板"""
        self.parameter_panel = QWidget()
        self.parameter_panel.setMaximumWidth(350)
        self.parameter_panel.setMinimumWidth(300)
        
        layout = QVBoxLayout(self.parameter_panel)
        layout.setContentsMargins(5, 5, 5, 5)
        
        title_label = QLabel("炮孔参数")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # Basic Info Group
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout()
        self.info_hole_id = QLabel()
        self.info_design_depth = QLabel()
        self.info_hole_type = QLabel()
        self.info_drilling_equipment = QLabel()
        self.info_diameter = QLabel()
        self.info_rock_type = QLabel()
        self.info_explosive_type = QLabel()
        self.info_explosive_amount = QLabel()
        basic_layout.addRow("炮孔编号:", self.info_hole_id)
        basic_layout.addRow("设计孔深:", self.info_design_depth)
        basic_layout.addRow("孔类型:", self.info_hole_type)
        basic_layout.addRow("打孔设备:", self.info_drilling_equipment)
        basic_layout.addRow("孔径:", self.info_diameter)
        basic_layout.addRow("岩石类型:", self.info_rock_type)
        basic_layout.addRow("炸药类型:", self.info_explosive_type)
        basic_layout.addRow("炸药量:", self.info_explosive_amount)
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)

        # Coordinates Group
        coords_group = QGroupBox("坐标信息")
        coords_layout = QFormLayout()
        self.info_x_coord = QLabel()
        self.info_y_coord = QLabel()
        self.info_z_coord = QLabel()
        coords_layout.addRow("X坐标:", self.info_x_coord)
        coords_layout.addRow("Y坐标:", self.info_y_coord)
        coords_layout.addRow("Z坐标:", self.info_z_coord)
        coords_group.setLayout(coords_layout)
        layout.addWidget(coords_group)

        # Blasting Parameters Group
        params_group = QGroupBox("孔网参数")
        params_layout = QFormLayout()
        self.info_spacing = QLabel()
        self.info_row_spacing = QLabel()
        self.info_bench_height = QLabel()
        self.info_measured_depth = QLabel()
        self.info_backfill_length = QLabel()
        params_layout.addRow("间距:", self.info_spacing)
        params_layout.addRow("排距:", self.info_row_spacing)
        params_layout.addRow("段高:", self.info_bench_height)
        params_layout.addRow("实测孔深:", self.info_measured_depth)
        params_layout.addRow("回填长度:", self.info_backfill_length)
        params_group.setLayout(params_layout)
        layout.addWidget(params_group)
        
        layout.addStretch()
    
    def add_grid_background(self):
        """添加网格背景"""
        pen = QPen(QColor(200, 200, 200), 1, Qt.DotLine)
        
        # 绘制网格线
        for x in range(-1000, 1001, 50):
            line = self.graphics_scene.addLine(x, -1000, x, 1000, pen)
            line.setZValue(-1)
        
        for y in range(-1000, 1001, 50):
            line = self.graphics_scene.addLine(-1000, y, 1000, y, pen)
            line.setZValue(-1)
    
    def set_mode(self, mode):
        """设置操作模式"""
        if mode == "table":
            self.stacked_widget.setCurrentIndex(1)
            # 更新表格数据
            self.update_table_data()
        else:
            self.stacked_widget.setCurrentIndex(0)
            if mode == "explosive_amount_edit":
                # 进入炸药量编辑模式
                self.explosive_amount_edit_mode = True
                self.graphics_view.set_mode(mode)
                print("DesignArea: 已进入炸药量编辑模式")
            elif mode == "normal":
                # 退出特殊模式
                self.explosive_amount_edit_mode = False
                self.graphics_view.set_mode(mode)
                print("DesignArea: 已退出炸药量编辑模式")
            elif mode != "graphics":  # graphics模式不需要传递给graphics_view
                self.graphics_view.set_mode(mode)
    
    def get_current_mode(self):
        """获取当前视图模式"""
        if hasattr(self, 'graphics_view'):
            return self.graphics_view.current_mode
        return "unknown"
    
    def add_blast_hole(self, x, y, diameter=10):
        """添加炮孔到设计区域"""
        # 创建炮孔 - BlastHoleItem类已在本文件中定义
        hole = BlastHoleItem(x, y, diameter)

        # 为了向后兼容，保留此方法但简化实现
        # 生成炮孔编号
        holes = self.get_blast_holes()
        hole_count = len(holes)
        hole_id = f"H{hole_count + 1:03d}"
        hole.hole_id = hole_id

        # 设置孔径值
        if diameter > 40: # 假设大于40的是毫米值
            hole.diameter_value = diameter
        else:
            hole.diameter_value = 110.0 # 默认110mm
        
        # 初始化精细化设计属性（如果不存在的话）
        if not hasattr(hole, 'special_hole_type'):
            hole.special_hole_type = "普通孔"
        if not hasattr(hole, 'drilling_equipment'):
            hole.drilling_equipment = "默认设备"
        if not hasattr(hole, 'equipment_diameter'):
            hole.equipment_diameter = hole.diameter_value
            
        hole.update_text_label()
        
        # 添加到场景中
        self.graphics_scene.addItem(hole)
        
        # 更新场景
        self.graphics_scene.update()
        
        print(f"炮孔 {hole.hole_id} 已添加到场景")
        return hole
    
    def add_boundary_line(self, x1, y1, x2, y2):
        """添加边界线"""
        line = BoundaryLineItem(x1, y1, x2, y2)
        line.item_type = "boundary_line"
        self.graphics_scene.addItem(line)
        self.save_state()
        return line
    
    def add_work_surface(self, points):
        """添加作业面"""
        surface = WorkSurfaceItem(points)
        self.graphics_scene.addItem(surface)
        self.save_state()
        return surface
    
    def add_text(self, x, y, text):
        """添加文本"""
        text_item = self.graphics_scene.addText(text, QFont("Arial", 12))
        text_item.setPos(x, y)
        text_item.setFlag(QGraphicsItem.ItemIsMovable, True)
        text_item.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.save_state()
        return text_item
    
    def add_reference_point(self, x, y):
        """添加参考点"""
        point = self.graphics_scene.addEllipse(x-3, y-3, 6, 6, 
                                             QPen(Qt.red, 2), 
                                             QBrush(Qt.red))
        point.setFlag(QGraphicsItem.ItemIsMovable, True)
        point.setFlag(QGraphicsItem.ItemIsSelectable, True)
        self.save_state()
        return point
    
    def add_connection_line(self, x1, y1, x2, y2):
        """添加连接线"""
        line = ConnectionLineItem(x1, y1, x2, y2)
        self.graphics_scene.addItem(line)
        self.save_state()
        return line
    
    def get_blast_holes(self):
        """获取所有炮孔"""
        holes = []
        for item in self.graphics_scene.items():
            if isinstance(item, BlastHoleItem):
                holes.append(item)
        return holes
    
    # 炮孔初步设计功能
    def start_add_hole_mode(self):
        """开始添加炮孔模式"""
        self.graphics_view.set_hole_operation_mode("add")
        print("进入添加炮孔模式，点击设计图添加炮孔")
    
    def start_delete_hole_mode(self):
        """开始删除炮孔模式"""
        self.graphics_view.set_hole_operation_mode("delete")
        print("进入删除炮孔模式，点击要删除的炮孔")
    
    def start_edit_hole_mode(self):
        """开始编辑炮孔模式"""
        self.graphics_view.set_hole_operation_mode("edit")
        print("进入编辑炮孔模式，点击要编辑的炮孔")
    
    def start_move_hole_mode(self):
        """开始移动炮孔模式"""
        self.graphics_view.set_hole_operation_mode("move")
        print("进入移动炮孔模式，拖动炮孔到新位置")
    
    def clear_all_holes(self):
        """清空所有炮孔"""
        from PyQt5.QtWidgets import QMessageBox
        
        holes = self.get_blast_holes()
        if not holes:
            return
        
        reply = QMessageBox.question(self, "确认清空", 
                                   f"确定要删除所有 {len(holes)} 个炮孔吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            print(f"开始清空 {len(holes)} 个炮孔...")
            
            # 先清除所有连接线（一次性清除，避免重复操作）
            connection_lines_removed = 0
            items_to_remove = []
            for item in self.graphics_scene.items():
                if hasattr(item, 'item_type') and item.item_type == "connection_line":
                    items_to_remove.append(item)
                    connection_lines_removed += 1
            
            for line in items_to_remove:
                self.graphics_scene.removeItem(line)
            
            if connection_lines_removed > 0:
                print(f"清除了 {connection_lines_removed} 条连接线")
            
            # 然后删除炮孔及其关联的文本标签
            for hole in holes:
                if hasattr(hole, 'text_item') and hole.text_item:
                    self.graphics_scene.removeItem(hole.text_item)
                if hasattr(hole, 'diameter_text') and hole.diameter_text:
                    self.graphics_scene.removeItem(hole.diameter_text)
                self.graphics_scene.removeItem(hole)
            
            print(f"已清空 {len(holes)} 个炮孔和 {connection_lines_removed} 条连接线")
            # 保存清空后的状态
            self.save_state()
    
    def add_hole_by_coordinate(self):
        """通过坐标添加炮孔"""
        try:
            from ..dialogs.hole_design_dialogs import CoordinateInputDialog
        except ImportError:
            QMessageBox.warning(self, "错误", "无法导入坐标输入对话框")
            return
        
        dialog = CoordinateInputDialog(self)
        if dialog.exec_() == dialog.Accepted:
            x, y, z = dialog.get_coordinates()
            
            # 生成炮孔编号
            holes = self.get_blast_holes()
            hole_count = len(holes) + 1
            hole_id = f"H{hole_count:03d}"
            
            # 创建炮孔，使用标准大小20像素（对应110mm）
            hole = BlastHoleItem(x, y, 20)
            hole.hole_id = hole_id
            hole.diameter_value = 110.0  # 设置默认孔径为110mm
            hole.z_coord = z
            hole.update_text_label()
            
            # 添加到场景
            self.graphics_scene.addItem(hole)
            print(f"已通过坐标添加炮孔: {hole_id} ({x}, {y}, {z})")
            
            # 保存状态到历史记录
            self.save_state()
    
    def modify_hole_coordinates(self):
        """修改炮孔坐标"""
        selected_items = self.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if isinstance(item, BlastHoleItem)]
        
        if not hole_items:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "请先选择要修改的炮孔")
            return
        
        # 如果选择了多个炮孔，只处理第一个
        hole_item = hole_items[0]
        self.graphics_view.edit_hole_item(hole_item)
    
    def modify_hole_data(self):
        """修改炮孔数据"""
        selected_items = self.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if isinstance(item, BlastHoleItem)]
        
        if not hole_items:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "请先选择要修改的炮孔")
            return
        
        # 如果选择了多个炮孔，只处理第一个
        hole_item = hole_items[0]
        self.graphics_view.edit_hole_item(hole_item)
    
    def modify_hole_avv(self):
        """修改炮孔AVV参数"""
        selected_items = self.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if isinstance(item, BlastHoleItem)]
        
        if not hole_items:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.information(self, "提示", "请先选择要修改AVV参数的炮孔")
            return
        
        from ..dialogs.hole_design_dialogs import AVVParameterDialog
        
        # 获取当前AVV参数（使用第一个选中炮孔的参数）
        hole_item = hole_items[0]
        current_params = {
            'spacing': hole_item.spacing,
            'row_spacing': hole_item.row_spacing,
            'bench_height': hole_item.bench_height,
            'over_drill': hole_item.over_drill
        }
        
        dialog = AVVParameterDialog(current_params, self)
        if dialog.exec_() == dialog.Accepted:
            new_params = dialog.get_parameters()
            
            # 更新所有选中炮孔的AVV参数
            for hole_item in hole_items:
                hole_item.spacing = new_params['spacing']
                hole_item.row_spacing = new_params['row_spacing']
                hole_item.bench_height = new_params['bench_height']
                # over_drill参数不在对话框中，使用默认值或保持原值
                if not hasattr(hole_item, 'over_drill'):
                    hole_item.over_drill = 1.0  # 默认超钻1米
            
            print(f"已更新 {len(hole_items)} 个炮孔的AVV参数")
    
    def exit_hole_operation_mode(self):
        """退出炮孔操作模式"""
        self.graphics_view.hole_operation_mode = None
        self.graphics_view.setCursor(Qt.ArrowCursor)
        print("已退出炮孔操作模式")
    
    def update_table_data(self):
        """更新表格数据"""
        holes = self.get_blast_holes()
        self.table_widget.setRowCount(len(holes))
        
        for row, hole in enumerate(holes):
            self.table_widget.setItem(row, 0, QTableWidgetItem(hole.hole_id))
            self.table_widget.setItem(row, 1, QTableWidgetItem(f"{hole.pos().x():.2f}"))
            self.table_widget.setItem(row, 2, QTableWidgetItem(f"{hole.pos().y():.2f}"))
            self.table_widget.setItem(row, 3, QTableWidgetItem(f"{hole.depth:.2f}"))
            self.table_widget.setItem(row, 4, QTableWidgetItem(f"{hole.diameter_value:.1f}"))
            self.table_widget.setItem(row, 5, QTableWidgetItem(hole.rock_type))
            self.table_widget.setItem(row, 6, QTableWidgetItem(hole.explosive_type))
            self.table_widget.setItem(row, 7, QTableWidgetItem(getattr(hole, 'charge_structure', '连续装药')))
            self.table_widget.setItem(row, 8, QTableWidgetItem(f"{hole.explosive_amount:.2f}"))
    
    def clear_design(self):
        """清空设计"""
        self.graphics_scene.clear()
        self.add_grid_background()
        self.table_widget.setRowCount(0)
        self.history.clear()
        self.history_index = -1
        # 保存初始状态
        self.save_state()
    
    def save_state(self):
        """保存当前状态到历史记录"""
        import json
        
        # 收集当前场景中的所有对象状态
        state = {
            'holes': [],
            'boundaries': [],
            'texts': [],
            'references': [],
            'work_surfaces': [],
            'connections': []
        }
        
        # 保存所有炮孔
        for item in self.graphics_scene.items():
            if isinstance(item, BlastHoleItem):
                state['holes'].append({
                    'hole_id': item.hole_id,
                    'hole_type': getattr(item, 'hole_type', '主炮孔'),
                    'x': item.pos().x(),
                    'y': item.pos().y(),
                    'diameter': getattr(item, 'diameter_value', 110.0),
                    'depth': getattr(item, 'depth', 15.0),
                    'rock_type': getattr(item, 'rock_type', '花岗岩'),
                    'explosive_type': getattr(item, 'explosive_type', '2号岩石铵梯炸药'),
                    'explosive_amount': getattr(item, 'explosive_amount', 25.0),
                    'charge_structure': getattr(item, 'charge_structure', '连续装药'),
                    'layout_mode': getattr(item, 'layout_mode', '矩形'),
                    'special_hole_type': getattr(item, 'special_hole_type', '普通孔'),
                    'drilling_equipment': getattr(item, 'drilling_equipment', '默认设备'),
                    'equipment_diameter': getattr(item, 'equipment_diameter', 110.0),
                    'x_coord': getattr(item, 'x_coord', item.pos().x()),
                    'y_coord': getattr(item, 'y_coord', item.pos().y()),
                    'z_coord': getattr(item, 'z_coord', 100.0),
                    'inclination': getattr(item, 'inclination', 0.0),
                    'azimuth': getattr(item, 'azimuth', 0.0),
                    'spacing': getattr(item, 'spacing', 3.5),
                    'row_spacing': getattr(item, 'row_spacing', 4.0),
                    'bench_height': getattr(item, 'bench_height', 15.0),
                    'over_drill': getattr(item, 'over_drill', 1.0),
                    'stemming_length': getattr(item, 'stemming_length', 3.0),
                    'charge_length': getattr(item, 'charge_length', 12.0)
                })
            elif isinstance(item, BoundaryLineItem):
                line = item.line()
                state['boundaries'].append({
                    'start': [line.x1(), line.y1()],
                    'end': [line.x2(), line.y2()]
                })
            elif isinstance(item, ConnectionLineItem):
                line = item.line()
                state['connections'].append({
                    'start': [line.x1(), line.y1()],
                    'end': [line.x2(), line.y2()],
                    'hole1_id': getattr(item, 'hole1_id', None),
                    'hole2_id': getattr(item, 'hole2_id', None),
                    'connection_type': getattr(item, 'connection_type', 'manual')
                })
            elif isinstance(item, WorkSurfaceItem):
                 state['work_surfaces'].append({
                    'points': [[p.x(), p.y()] for p in item.points]
                })
            elif isinstance(item, BoundaryPolygonItem):
                state['boundaries'].append({
                    'type': 'polygon',
                    'points': [[p.x(), p.y()] for p in item.points]
                })
            elif isinstance(item, QGraphicsTextItem) and not isinstance(item.parentItem(), BlastHoleItem):
                # 只保存非炮孔子项的文本
                 if hasattr(item, 'item_type') and item.item_type == 'text_annotation':
                    state['texts'].append({
                        'x': item.pos().x(),
                        'y': item.pos().y(),
                        'text': item.toPlainText(),
                        'item_type': 'text_annotation'
                    })
            elif isinstance(item, QGraphicsEllipseItem) and not isinstance(item.parentItem(), BlastHoleItem):
                 if hasattr(item, 'item_type') and item.item_type == 'reference_point':
                    state['references'].append({
                        'x': item.pos().x() + 3,  # 中心点坐标
                        'y': item.pos().y() + 3,
                        'label': '参考点'
                    })
        
        # 清理超出最大历史记录数的旧记录
        if self.history_index >= 0 and self.history_index < len(self.history) - 1:
            self.history = self.history[:self.history_index + 1]
        
        self.history.append(json.dumps(state))
        self.history_index += 1
        
        # 限制历史记录数量
        if len(self.history) > self.max_history:
            self.history = self.history[-self.max_history:]
            self.history_index = len(self.history) - 1
        
        # 通知主窗口更新撤销/重做按钮状态
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'update_undo_redo_status'):
            main_window.update_undo_redo_status()
    
    def undo(self):
        """撤销操作"""
        if self.history_index > 0:
            self.history_index -= 1
            self._restore_state(self.history[self.history_index])
            print(f"撤销操作 - 当前历史索引: {self.history_index}")
            
            # 通知主窗口更新撤销/重做按钮状态
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'update_undo_redo_status'):
                main_window.update_undo_redo_status()
    
    def redo(self):
        """重做操作"""
        if self.history_index < len(self.history) - 1:
            self.history_index += 1
            self._restore_state(self.history[self.history_index])
            print(f"重做操作 - 当前历史索引: {self.history_index}")
            
            # 通知主窗口更新撤销/重做按钮状态
            main_window = self.get_main_window()
            if main_window and hasattr(main_window, 'update_undo_redo_status'):
                main_window.update_undo_redo_status()
    
    def _restore_state(self, state_json):
        """恢复场景状态"""
        import json
        
        try:
            state = json.loads(state_json)
            
            # 清空当前场景（但保留网格）
            items_to_remove = []
            for item in self.graphics_scene.items():
                # 检查是否是网格线（网格线Z值为-1）
                if not (hasattr(item, 'zValue') and item.zValue() == -1):
                    items_to_remove.append(item)
            
            for item in items_to_remove:
                self.graphics_scene.removeItem(item)
            
            # 恢复炮孔
            if 'holes' in state:
                for hole_data in state.get('holes', []):
                    diameter = hole_data.get('diameter', 110.0)
                    # 恢复时，使用像素直径来创建
                    visual_size = max(8, min(40, diameter * 20 / 110))
                    hole = BlastHoleItem(hole_data['x'], hole_data['y'], visual_size)
                    
                    # 设置完整的炮孔数据
                    hole_data_copy = hole_data.copy()
                    hole.set_data(hole_data_copy)
                    
                    self.graphics_scene.addItem(hole)
            
            # 恢复边界线
            if 'boundaries' in state:
                for boundary_data in state['boundaries']:
                    if boundary_data.get('type') == 'polygon':
                        points = [QPointF(x, y) for x, y in boundary_data['points']]
                        boundary = BoundaryPolygonItem(points)
                        boundary.item_type = "boundary"
                        self.graphics_scene.addItem(boundary)
                    else: # 兼容旧格式
                        start = boundary_data['start']
                        end = boundary_data['end']
                        line = BoundaryLineItem(start[0], start[1], end[0], end[1])
                        self.graphics_scene.addItem(line)
            
            # 恢复作业面
            if 'work_surfaces' in state:
                 for ws_data in state['work_surfaces']:
                    points = [QPointF(x, y) for x, y in ws_data['points']]
                    surface = WorkSurfaceItem(points)
                    self.graphics_scene.addItem(surface)

            # 恢复独立文本标注
            if 'texts' in state:
                for text_data in state.get('texts', []):
                    text_item = QGraphicsTextItem(text_data['text'])
                    text_item.setPos(text_data['x'], text_data['y'])
                    text_item.setFlag(QGraphicsItem.ItemIsMovable, True)
                    text_item.setFlag(QGraphicsItem.ItemIsSelectable, True)
                    text_item.setFont(QFont("SimHei", 10))
                    text_item.setDefaultTextColor(Qt.black)
                    # 确保恢复的是独立文本，而不是炮孔标签
                    if 'item_type' in text_data and text_data['item_type'] == 'text_annotation':
                        text_item.item_type = "text_annotation"
                        self.graphics_scene.addItem(text_item)

            # 恢复参考点
            if 'references' in state:
                for ref_data in state.get('references', []):
                    point = self.graphics_scene.addEllipse(
                        ref_data['x'] - 3, ref_data['y'] - 3, 6, 6,
                        QPen(Qt.red, 2), QBrush(Qt.red)
                    )
                    point.setFlag(QGraphicsItem.ItemIsMovable, True)
                    point.setFlag(QGraphicsItem.ItemIsSelectable, True)
                    point.item_type = 'reference_point'

            # 恢复连接线
            if 'connections' in state:
                for conn_data in state.get('connections', []):
                    start = conn_data['start']
                    end = conn_data['end']
                    line = ConnectionLineItem(start[0], start[1], end[0], end[1])
                    if conn_data.get('hole1_id') and conn_data.get('hole2_id'):
                        line.set_hole_connection(
                            conn_data['hole1_id'], 
                            conn_data['hole2_id'],
                            conn_data.get('connection_type', 'manual')
                        )
                    self.graphics_scene.addItem(line)
            
            # 更新表格数据
            self.update_table_data()
            
        except Exception as e:
            print(f"恢复状态失败: {str(e)}")
    
    def zoom_in(self):
        """放大"""
        self.graphics_view.zoom_in()
        self.zoom_changed.emit(self.graphics_view.zoom_factor * 100)
    
    def zoom_out(self):
        """缩小"""
        self.graphics_view.zoom_out()
        self.zoom_changed.emit(self.graphics_view.zoom_factor * 100)
    
    def fit_view(self):
        """适应全图"""
        self.graphics_view.fit_view()
        self.zoom_changed.emit(100)
    
    def on_mouse_moved(self, scene_pos):
        """鼠标移动事件处理"""
        self.coordinates_changed.emit(scene_pos.x(), scene_pos.y())
    
    def on_selection_changed(self):
        """当选择发生变化时更新参数显示"""
        selected_items = self.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if isinstance(item, BlastHoleItem)]
        
        if len(hole_items) == 1:
            self.show_hole_parameters(hole_items[0])
        elif len(hole_items) > 1:
            self.show_multiple_holes_info(hole_items)
        else:
            self.clear_parameter_display()

    def show_hole_parameters(self, hole_item):
        """显示单个炮孔的参数"""
        data = hole_item.get_data()
        self.info_hole_id.setText(data.get('hole_id', 'N/A'))
        self.info_design_depth.setText(f"{data.get('depth', 0.0):.1f} m")
        self.info_hole_type.setText(data.get('hole_type', 'N/A'))
        self.info_drilling_equipment.setText(data.get('drilling_equipment', 'N/A'))
        self.info_diameter.setText(f"{data.get('diameter', 0.0):.0f} mm")
        self.info_rock_type.setText(data.get('rock_type', 'N/A'))
        self.info_explosive_type.setText(data.get('explosive_type', 'N/A'))
        self.info_explosive_amount.setText(f"{data.get('explosive_amount', 0.0):.2f} kg")

        self.info_x_coord.setText(f"{data.get('x', 0.0):.2f}")
        self.info_y_coord.setText(f"{data.get('y', 0.0):.2f}")
        self.info_z_coord.setText(f"{data.get('z', 0.0):.2f}")

        self.info_spacing.setText(f"{data.get('spacing', 0.0):.1f} m")
        self.info_row_spacing.setText(f"{data.get('row_spacing', 0.0):.1f} m")
        self.info_bench_height.setText(f"{data.get('bench_height', 0.0):.1f} m")
        self.info_measured_depth.setText(f"{data.get('depth', 0.0):.1f} m") # Assuming measured depth is same as design for now
        self.info_backfill_length.setText(f"{data.get('backfill_length', 0.0):.1f} m")

    def show_multiple_holes_info(self, hole_items):
        """显示多个炮孔的统计信息"""
        self.clear_parameter_display()
        self.info_hole_id.setText(f"选中 {len(hole_items)} 个炮孔")

    def clear_parameter_display(self):
        """清空参数显示"""
        self.info_hole_id.setText("")
        self.info_design_depth.setText("")
        self.info_hole_type.setText("")
        self.info_drilling_equipment.setText("")
        self.info_diameter.setText("")
        self.info_rock_type.setText("")
        self.info_explosive_type.setText("")
        self.info_explosive_amount.setText("")
        self.info_x_coord.setText("")
        self.info_y_coord.setText("")
        self.info_z_coord.setText("")
        self.info_spacing.setText("")
        self.info_row_spacing.setText("")
        self.info_bench_height.setText("")
        self.info_measured_depth.setText("")
        self.info_backfill_length.setText("")

    def set_layout_mode(self, mode, layout_data):
        """设置智能布孔模式"""
        self.layout_mode = mode
        self.layout_data = layout_data
        self.layout_points = []
        
        # 检查是否有预设坐标
        points_data = layout_data.get('adjusted_params', {}).get('points', [])
        valid_points = [p for p in points_data if p != (0, 0)]

        if self.layout_mode == "一排" and len(valid_points) == 2:
            self.layout_points = [QPointF(p[0], p[1]) for p in valid_points]
            self.create_single_row_holes()
            self.exit_layout_mode()
        elif self.layout_mode == "爆区" and len(valid_points) == 4:
            self.layout_points = [QPointF(p[0], p[1]) for p in valid_points]
            self.create_blast_area_holes()
            self.exit_layout_mode()
        else:
            # 进入手动布点模式
            self.graphics_view.setCursor(Qt.CrossCursor)
            if self.parent() and hasattr(self.parent(), 'update_status'):
                if self.layout_mode == "一排":
                    self.parent().update_status("请在图上点击选择布孔的起点和终点")
                else:
                    self.parent().update_status("请在图上点击选择爆区的四个角点")

    def handle_layout_click(self, scene_pos):
        """处理智能布孔点击事件"""
        self.layout_points.append(scene_pos)
        
        # 绘制临时点
        temp_point = self.graphics_scene.addEllipse(scene_pos.x()-2, scene_pos.y()-2, 4, 4, QPen(Qt.green), QBrush(Qt.green))
        temp_point.setZValue(1000)

        if self.layout_mode == "一排" and len(self.layout_points) == 2:
            self.create_single_row_holes()
            self.exit_layout_mode()
        elif self.layout_mode == "爆区" and len(self.layout_points) == 4:
            self.create_blast_area_holes()
            self.exit_layout_mode()
        
        return True

    def create_single_row_holes(self):
        """根据起点和终点生成一排炮孔"""
        if len(self.layout_points) < 2:
            return

        p1, p2 = self.layout_points[0], self.layout_points[1]
        distance = math.sqrt((p2.x() - p1.x())**2 + (p2.y() - p1.y())**2)
        
        if distance == 0:
            return

        hole_spacing = self.layout_data['adjusted_params'].get('hole_spacing', 5.0)
        if hole_spacing <= 0:
            return

        # 将米转换为像素单位 (1米 = 50像素)
        pixel_spacing = hole_spacing * 50
        num_holes = int(distance / pixel_spacing) + 1
        
        # 计算单位向量
        unit_vec = (p2 - p1) / distance
        
        # 使用精确间距生成炮孔
        for i in range(num_holes):
            pos = p1 + unit_vec * (i * pixel_spacing)
            # 防止超出终点
            if (p1 - pos).length() > distance + 1e-6:
                 break
            self._create_intelligent_hole(pos.x(), pos.y(), self.layout_data['input_params'], self.layout_data['results'].get('explosive_amount', 25.0))

        # 保存状态
        self.save_state()
        if self.parent():
            self.parent().update_status(f"已生成 {num_holes} 个炮孔")

    def create_blast_area_holes(self):
        """根据四个角点和参数生成爆区炮孔"""
        if len(self.layout_points) < 4:
            return
            
        points = self.layout_points
        
        params = self.layout_data.get('adjusted_params', {})
        hole_spacing = params.get('hole_spacing', 5.0)
        row_spacing = params.get('row_spacing', 5.0)
        
        if hole_spacing <= 0 or row_spacing <= 0:
            return
            
        layout_mode = self.layout_data.get('input_params', {}).get('layout_mode', '矩形布孔')

        # 定义两个基准向量 (p1->p2为排方向, p1->p4为列方向)
        vec_row_dir = points[1] - points[0]
        vec_col_dir = points[3] - points[0]
        
        row_dist = vec_row_dir.length()
        col_dist = vec_col_dir.length()

        if row_dist == 0 or col_dist == 0: return

        # 单位向量
        unit_vec_row = vec_row_dir / row_dist
        unit_vec_col = vec_col_dir / col_dist

        # 将米转换为像素单位 (1米 = 50像素)
        pixel_hole_spacing = hole_spacing * 50
        pixel_row_spacing = row_spacing * 50

        num_holes_per_row = int(row_dist / pixel_hole_spacing) + 1
        num_rows = int(col_dist / pixel_row_spacing) + 1
        
        added_count = 0
        for r in range(num_rows):
            for h in range(num_holes_per_row):
                # 计算当前点的位置
                pos = points[0] + (unit_vec_row * h * pixel_hole_spacing) + (unit_vec_col * r * pixel_row_spacing)

                # 三角形布孔模式下，奇数排进行半个孔距的偏移
                if layout_mode == '三角形布孔' and r % 2 != 0:
                    pos += unit_vec_row * (pixel_hole_spacing / 2)

                # 检查点是否在四边形内 (近似)
                # 此处简化处理，假设为凸四边形，并且我们的生成不会超出太多
                # 一个更鲁棒的方法是检查所有边的叉积

                self._create_intelligent_hole(pos.x(), pos.y(), self.layout_data['input_params'], self.layout_data['results'].get('explosive_amount', 25.0))
                added_count += 1
        
        # 保存状态
        self.save_state()
        if self.parent():
            self.parent().update_status(f"已生成 {added_count} 个炮孔")

    def exit_layout_mode(self):
        """退出智能布孔模式"""
        self.layout_mode = None
        self.layout_data = None
        self.layout_points = []
        self.graphics_view.setCursor(Qt.ArrowCursor)
        
        if hasattr(self.parent(), 'update_status'):
            self.parent().update_status("已退出智能布孔模式")

    def exit_fine_design_mode(self):
        """退出精细化设计模式"""
        self.special_hole_mode = None
        self.drilling_mode = False
        print("已退出精细化设计模式")
    
    def set_detailed_design_mode(self, mode, settings=None):
        """设置精细化设计模式"""
        self.detailed_design_mode = mode
        self.detailed_design_settings = settings or {}
        
        # 确保主窗口引用存在
        if not self.main_window:
            self.main_window = self.get_main_window()
        
        # 设置图形视图的点击处理
        print(f"设置图形视图的精细化设计模式...")
        print(f"graphics_view对象: {self.graphics_view}")
        print(f"hasattr(self.graphics_view, 'detailed_design_mode'): {hasattr(self.graphics_view, 'detailed_design_mode')}")
        
        if hasattr(self.graphics_view, 'detailed_design_mode'):
            print(f"在graphics_view上设置detailed_design_mode = {mode}")
            self.graphics_view.detailed_design_mode = mode
            self.graphics_view.detailed_design_settings = self.detailed_design_settings
            print(f"设置完成，验证: graphics_view.detailed_design_mode = {self.graphics_view.detailed_design_mode}")
            
            # 设置鼠标光标
            if mode == 'rock':
                self.graphics_view.setCursor(Qt.PointingHandCursor)
                print("设置鼠标光标为PointingHandCursor")
            elif mode == 'explosive':
                self.graphics_view.setCursor(Qt.CrossCursor)
                print("设置鼠标光标为CrossCursor")
            else:
                self.graphics_view.setCursor(Qt.ArrowCursor)
                print("设置鼠标光标为ArrowCursor")
        else:
            print("ERROR: graphics_view没有detailed_design_mode属性!")
        
        print(f"设置精细化设计模式: {mode}")
        print(f"主窗口对象: {self.main_window}")
        print(f"设置参数: {self.detailed_design_settings}")
    
    def exit_detailed_design_mode(self):
        """退出精细化设计模式"""
        self.detailed_design_mode = None
        self.detailed_design_settings = {}
        
        if hasattr(self.graphics_view, 'detailed_design_mode'):
            self.graphics_view.detailed_design_mode = None
            self.graphics_view.detailed_design_settings = {}
            self.graphics_view.setCursor(Qt.ArrowCursor)
    
    def save_initial_state(self):
        """保存初始状态"""
        self.save_state()
    
    def get_main_window(self):
        """获取主窗口对象"""
        if self.main_window:
            return self.main_window
        
        # 从父级查找主窗口
        widget = self
        while widget:
            parent = widget.parent()
            if parent and hasattr(parent, '__class__'):
                if parent.__class__.__name__ == 'MainWindow':
                    self.main_window = parent
                    return parent
                elif hasattr(parent, 'set_rock_type_for_selected_hole') and hasattr(parent, 'current_rock_type'):
                    self.main_window = parent
                    return parent
            widget = parent
        
        return None
    
    def set_main_window(self, main_window):
        """设置主窗口引用"""
        self.main_window = main_window
    
    # 绘制模式方法
    def start_work_surface_mode(self):
        """开始作业面绘制模式"""
        self.graphics_view.drawing_mode = "work_surface"
        self.graphics_view.drawing_points = []
        self.graphics_view.setCursor(Qt.CrossCursor)
        print("进入作业面绘制模式")
        if hasattr(self.parent(), 'update_status'):
            self.parent().update_status("作业面绘制模式：左键点击绘制点，右键结束绘制")
    
    def start_boundary_mode(self):
        """开始边界线绘制模式"""
        self.graphics_view.drawing_mode = "boundary"
        self.graphics_view.drawing_points = []
        self.graphics_view.setCursor(Qt.CrossCursor)
        print("进入边界线绘制模式")
        if hasattr(self.parent(), 'update_status'):
            self.parent().update_status("边界线绘制模式：左键点击绘制点，右键结束绘制并自动闭合")
    
    def start_text_mode(self):
        """开始文本添加模式"""
        self.graphics_view.drawing_mode = "text"
        self.graphics_view.drawing_points = []
        self.graphics_view.setCursor(Qt.IBeamCursor)
        print("进入文本添加模式")
        if hasattr(self.parent(), 'update_status'):
            self.parent().update_status("文本添加模式：点击位置输入文本")
    
    def start_reference_point_mode(self):
        """开始参考点添加模式"""
        self.graphics_view.drawing_mode = "reference_point"
        self.graphics_view.drawing_points = []
        self.graphics_view.setCursor(Qt.PointingHandCursor)
        print("进入参考点添加模式")
        if hasattr(self.get_main_window(), 'update_status'):
            self.get_main_window().update_status("参考点添加模式：点击位置添加参考点")
    
    def clear_screen(self):
        """清空屏幕上的所有元素，此操作可撤销"""
        from PyQt5.QtWidgets import QMessageBox
        from PyQt5.QtCore import Qt

        # 检查场景中是否有非网格线项目
        items_to_check = [item for item in self.graphics_scene.items() if item.zValue() != -1]
        
        if not items_to_check:
            QMessageBox.information(self, "提示", "屏幕已经是空的。")
            return

        reply = QMessageBox.question(self, "确认清空", 
                                   "确定要清空屏幕上的所有元素吗？\n此操作可以被撤销。",
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            # 收集所有非网格线项目
            items_to_remove = [item for item in self.graphics_scene.items() if item.zValue() != -1]
            
            for item in items_to_remove:
                self.graphics_scene.removeItem(item)
            
            # 更新表格和状态
            self.update_table_data()
            self.save_state()
            
            main_window = self.get_main_window()
            if main_window:
                main_window.update_status("已清空所有元素")

    def start_hole_type_setting_mode(self):
        """开始孔类型设置模式"""
        if self.main_window:
            self.main_window.update_status("进入孔类型设置模式，请选择炮孔后从菜单设置类型")
        for item in self.get_blast_holes():
            item.set_extra_labels_visible(False)
        if self.main_window:
            self.main_window.set_hole_types()
        self.exit_hole_type_setting_mode()

    def exit_hole_type_setting_mode(self):
        """退出孔类型设置模式"""
        if self.main_window:
            self.main_window.update_status("已退出孔类型设置模式")
        for item in self.get_blast_holes():
            item.set_extra_labels_visible(True)

    def start_diameter_setting_mode(self):
        """开始孔径设置模式"""
        self.is_diameter_setting_mode = True
        if self.main_window:
            self.main_window.update_status("进入孔径设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def exit_diameter_setting_mode(self):
        """退出孔径设置模式"""
        self.is_diameter_setting_mode = False
        if self.main_window:
            self.main_window.update_status("已退出孔径设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def set_extra_labels_visible(self, visible):
        """设置除ID外的所有附加标签的可见性"""
        self.diameter_text.setVisible(visible)
        self.rock_text.setVisible(visible)
        self.explosive_text.setVisible(visible)
        self.spacing_text.setVisible(visible)
        self.explosive_amount_text.setVisible(visible)
        
    def update_label_visibility_and_content(self):
        """统一更新所有标签的可见性和内容"""
        design_area = self.get_design_area()
        
        # 默认只显示炮孔编号，其他标签隐藏
        is_rock_visible = False
        is_explosive_visible = False
        is_diameter_visible = False
        is_spacing_visible = False
        is_explosive_amount_visible = False

        # 只有在精细化设计模式下才显示对应的标签
        if design_area:
            if getattr(design_area, 'is_diameter_setting_mode', False):
                is_diameter_visible = True
            elif getattr(design_area, 'is_rock_setting_mode', False):
                is_rock_visible = True
            elif getattr(design_area, 'is_explosive_setting_mode', False):
                is_explosive_visible = True
            elif getattr(design_area, 'is_spacing_setting_mode', False):
                is_spacing_visible = True
            elif getattr(design_area, 'is_explosive_amount_setting_mode', False):
                is_explosive_amount_visible = True

        self.rock_text.setVisible(is_rock_visible)
        self.explosive_text.setVisible(is_explosive_visible)
        self.diameter_text.setVisible(is_diameter_visible)
        self.spacing_text.setVisible(is_spacing_visible)
        self.explosive_amount_text.setVisible(is_explosive_amount_visible)

        # 更新可见标签的内容
        self.text_item.setPlainText(self.hole_id)
        
        if is_diameter_visible:
            diameter_info = f"φ{self.diameter_value:.0f}mm"
            if hasattr(self, 'drilling_equipment') and self.drilling_equipment != "默认设备":
                diameter_info = f"{self.drilling_equipment}\nφ{self.equipment_diameter:.0f}mm"
            self.diameter_text.setPlainText(diameter_info)
            self.diameter_text.setPos(25, 25)

        if is_rock_visible:
            self.rock_text.setPlainText(self.rock_type)
            color_map = {'花岗岩': Qt.darkRed, '石灰岩': Qt.darkBlue, '砂岩': Qt.darkYellow, '页岩': Qt.darkGray, '玄武岩': Qt.black, '大理岩': Qt.darkCyan, '片麻岩': Qt.darkMagenta, '辉绿岩': Qt.darkGreen}
            self.rock_text.setDefaultTextColor(color_map.get(self.rock_type, Qt.darkGreen))
            rock_y_pos = 55 if '\n' in self.diameter_text.toPlainText() else 45
            self.rock_text.setPos(25, rock_y_pos)

        if is_explosive_visible:
            explosive_short = self.explosive_type.replace('号岩石铵梯炸药', '号铵梯').replace('炸药', '')
            structure_short = self.charge_structure.replace('装药', '')
            self.explosive_text.setPlainText(f"{explosive_short}\n{structure_short}")
            self.explosive_text.setDefaultTextColor(Qt.darkMagenta if self.charge_structure == "连续装药" else Qt.darkCyan)
            rock_y_pos = self.rock_text.pos().y()
            self.explosive_text.setPos(25, rock_y_pos + 25)

        if is_spacing_visible:
            spacing_info = f"A: {self.spacing:.1f}m\nV: {self.row_spacing:.1f}m"
            self.spacing_text.setPlainText(spacing_info)

        if is_explosive_amount_visible:
            self.explosive_amount_text.setPlainText(f"{self.explosive_amount:.2f} kg")
            # 在炸药量模式下，将此标签移动到主ID下方
            self.explosive_amount_text.setPos(10, 10)
        else:
            # 恢复默认位置
            self.explosive_amount_text.setPos(25, 55)

        if self.scene():
            expanded_rect = self.mapRectToScene(self.boundingRect()).adjusted(-100, -50, 100, 150)
            self.scene().update(expanded_rect)

    def start_rock_setting_mode(self):
        """开始岩石设置模式"""
        self.is_rock_setting_mode = True
        if self.main_window:
            self.main_window.update_status("进入岩石设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def exit_rock_setting_mode(self):
        """退出岩石设置模式"""
        self.is_rock_setting_mode = False
        if self.main_window:
            self.main_window.update_status("已退出岩石设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def start_explosive_setting_mode(self):
        """开始炸药设置模式"""
        self.is_explosive_setting_mode = True
        if self.main_window:
            self.main_window.update_status("进入炸药设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def exit_explosive_setting_mode(self):
        """退出炸药设置模式"""
        self.is_explosive_setting_mode = False
        if self.main_window:
            self.main_window.update_status("已退出炸药设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def start_spacing_setting_mode(self):
        """开始间排距设置模式"""
        self.is_spacing_setting_mode = True
        if self.main_window:
            self.main_window.update_status("进入间排距设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def exit_spacing_setting_mode(self):
        """退出间排距设置模式"""
        self.is_spacing_setting_mode = False
        if self.main_window:
            self.main_window.update_status("已退出间排距设置模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def start_explosive_amount_setting_mode(self):
        """开始编辑炸药量模式"""
        self.is_explosive_amount_setting_mode = True
        if self.main_window:
            self.main_window.update_status("进入编辑炸药量模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def exit_explosive_amount_setting_mode(self):
        """退出编辑炸药量模式"""
        self.is_explosive_amount_setting_mode = False
        if self.main_window:
            self.main_window.update_status("已退出编辑炸药量模式")
        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

    def set_default_view(self):
        """设置默认的简洁视图"""
        self.is_default_view_mode = True
        self.is_hole_type_mode = False
        self.is_diameter_setting_mode = False
        self.is_rock_setting_mode = False
        self.is_explosive_setting_mode = False
        self.is_spacing_setting_mode = False
        self.is_explosive_amount_setting_mode = False

        for item in self.get_blast_holes():
            item.update_label_visibility_and_content()

        if self.main_window:
            self.main_window.update_status("文件加载完成，已切换到简洁视图")

    def _create_intelligent_hole(self, x, y, input_params, explosive_amount):
        """创建智能布孔炮孔"""
        # 获取孔径参数
        diameter = input_params.get('diameter', 110.0)
        
        # 转换为像素大小 - 与手动添加炮孔保持一致
        if diameter > 40:  # 毫米值
            pixel_diameter = 20  # 固定为20像素，对应110mm
        else:
            pixel_diameter = diameter
        
        # 创建炮孔
        hole = BlastHoleItem(x, y, pixel_diameter)
        
        # 生成炮孔编号
        holes = self.get_blast_holes()
        hole_count = len(holes)
        hole_id = f"H{hole_count + 1:03d}"
        hole.hole_id = hole_id
        
        # 设置孔径值
        hole.diameter_value = diameter
        
        # 设置基本参数
        hole.rock_type = input_params.get('rock_type', '花岗岩')
        hole.explosive_type = input_params.get('explosive_type', '2号岩石铵梯炸药')
        hole.depth = input_params.get('design_depth', 15.0)
        hole.explosive_amount = explosive_amount
        hole.hole_type = "主炮孔"
        
        # 设置AVV参数
        hole.spacing = input_params.get('hole_spacing', 3.5)
        hole.row_spacing = input_params.get('row_spacing', 4.0)
        hole.bench_height = input_params.get('bench_height', 15.0)
        hole.over_drill = input_params.get('over_drill', 1.0)
        hole.stemming_length = input_params.get('backfill_length', 3.0)
        hole.charge_length = hole.depth - hole.stemming_length
        
        # 设置精细化设计属性
        hole.special_hole_type = "普通孔"
        hole.drilling_equipment = "默认设备"
        hole.equipment_diameter = diameter
        
        # 设置坐标
        hole.x_coord = x
        hole.y_coord = y
        hole.z_coord = input_params.get('blast_level', 100.0)
        
        # 根据布孔模式设置颜色
        layout_mode = input_params.get('layout_mode', '矩形布孔')
        if layout_mode == '三角形布孔':
            hole.setBrush(QBrush(QColor(255, 165, 0)))  # 橙色
            hole.layout_mode = '三角形'
        else:
            hole.setBrush(QBrush(Qt.yellow))  # 黄色
            hole.layout_mode = '矩形'
        
        # 更新显示
        hole.update_text_label()
        hole.update_rock_display()
        
        # 添加到场景
        self.graphics_scene.addItem(hole)
        
        return hole