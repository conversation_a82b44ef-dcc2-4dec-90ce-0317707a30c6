# -*- coding: utf-8 -*-
"""
智能布孔对话框 - 完整版
包含左侧示意图和右侧参数设置
"""

import sys
import os
import json
import math
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QLabel, QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox,
                             QPushButton, QTextEdit, QTabWidget, QWidget, QGroupBox,
                             QMessageBox, QTableWidget, QTableWidgetItem, QHeaderView,
                             QFrame, QButtonGroup, QRadioButton, QGridLayout)
from PyQt5.QtCore import Qt, pyqtSignal, QRectF
from PyQt5.QtGui import QFont, QPainter, QPen, QBrush, QColor, QPixmap
from PyQt5.QtWidgets import QGraphicsView, QGraphicsScene, QGraphicsRectItem, QGraphicsTextItem

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.blast_calculations import BlastCalculations

class HoleSchematicWidget(QFrame):
    """炮孔示意图组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(300, 400)
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("background-color: white; border: 1px solid gray;")
        
        # 示意图参数
        self.design_depth = 15.0
        self.backfill_length = 3.0
        self.min_stemming = 2.0
        self.bench_height = 12.0
        self.charge_length = 0.0
        
    def update_parameters(self, design_depth, backfill_length, min_stemming, bench_height):
        """更新示意图参数"""
        self.design_depth = design_depth
        self.backfill_length = backfill_length
        self.min_stemming = min_stemming
        self.bench_height = bench_height
        self.charge_length = design_depth - backfill_length
        self.update()
    
    def paintEvent(self, event):
        """绘制示意图"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制区域
        draw_rect = QRectF(20, 20, 260, 360)
        
        # 计算比例
        total_depth = max(self.design_depth, self.bench_height)
        if total_depth <= 0:
            return
        
        scale = 300 / total_depth
        
        # 绘制爆破水平线
        ground_y = 50
        painter.setPen(QPen(QColor(139, 69, 19), 3))  # 棕色
        painter.drawLine(20, int(ground_y), 280, int(ground_y))
        painter.drawText(220, int(ground_y - 5), "爆破水平")
        
        # 计算各段位置
        hole_start_y = ground_y
        stemming_end_y = hole_start_y + (self.design_depth - self.charge_length) * scale
        charge_end_y = hole_start_y + self.design_depth * scale
        bench_end_y = hole_start_y + self.bench_height * scale
        
        # 绘制炮孔
        hole_x = 150
        hole_width = 8
        
        # 填塞段（蓝色）
        stemming_length = self.design_depth - self.charge_length
        if stemming_length > 0:
            painter.setBrush(QBrush(QColor(100, 150, 255)))
            painter.setPen(QPen(QColor(0, 100, 200), 2))
            stemming_rect = QRectF(hole_x - hole_width/2, hole_start_y, hole_width, stemming_length * scale)
            painter.drawRect(stemming_rect)
            
            # 标注回填长度
            painter.setPen(QPen(Qt.blue, 1))
            painter.drawText(int(hole_x + 15), int(hole_start_y + stemming_length * scale / 2),
                           f"回填长度\n{stemming_length:.1f}m")
        
        # 装药段（红色）
        if self.charge_length > 0:
            painter.setBrush(QBrush(QColor(255, 100, 100)))
            painter.setPen(QPen(QColor(200, 0, 0), 2))
            charge_rect = QRectF(hole_x - hole_width/2, stemming_end_y, hole_width, self.charge_length * scale)
            painter.drawRect(charge_rect)
            
            # 标注装药长度
            painter.setPen(QPen(Qt.red, 1))
            painter.drawText(int(hole_x + 15), int(stemming_end_y + self.charge_length * scale / 2),
                           f"装药长度\n{self.charge_length:.1f}m")
        
        # 绘制最小填塞长度标线
        min_stemming_y = hole_start_y + self.min_stemming * scale
        painter.setPen(QPen(QColor(255, 165, 0), 2))  # 橙色
        painter.drawLine(int(hole_x - 20), int(min_stemming_y), int(hole_x + 20), int(min_stemming_y))
        painter.drawText(int(hole_x + 25), int(min_stemming_y + 5), f"最小填塞长度 {self.min_stemming:.2f}m")
        
        # 绘制台阶高度标线
        if self.bench_height > 0:
            painter.setPen(QPen(QColor(0, 128, 0), 2))  # 绿色
            painter.drawLine(int(hole_x + 30), int(ground_y), int(hole_x + 30), int(bench_end_y))
            painter.drawText(int(hole_x + 35), int(ground_y + self.bench_height * scale / 2),
                           f"台阶高度\n{self.bench_height:.1f}m")
        
        # 绘制设计孔深标线
        painter.setPen(QPen(Qt.black, 1))
        painter.drawLine(int(hole_x - 30), int(ground_y), int(hole_x - 30), int(charge_end_y))
        painter.drawText(int(hole_x - 55), int(ground_y + self.design_depth * scale / 2),
                       f"设计孔深\n{self.design_depth:.1f}m")
        
        # 绘制标题
        painter.setPen(QPen(Qt.black, 2))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(20, 15, "炮孔结构示意图")

class IntelligentLayoutDialog(QDialog):
    """智能布孔对话框"""
    
    layout_calculated = pyqtSignal(dict)  # 布孔计算完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("智能布孔")
        self.setModal(True)
        self.resize(900, 700)
        
        # 计算器实例
        self.calculator = BlastCalculations()
        
        # 计算结果
        self.calculation_results = {}
        
        # 当前布孔类型
        self.current_layout_type = "一排"
        
        # 从主窗口获取设计参数
        self.blast_level = getattr(parent, 'blast_level', 0.0)
        self.step_height = getattr(parent, 'step_height', 15.0)
        
        self.init_ui()
        self.load_parameters()
        self.connect_signals()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 布孔类型选择
        type_layout = QHBoxLayout()
        type_label = QLabel("布孔类型:")
        type_label.setFont(QFont("Arial", 10, QFont.Bold))
        
        self.type_group = QButtonGroup()
        self.single_row_radio = QRadioButton("一排")
        self.blast_area_radio = QRadioButton("爆区")
        self.single_row_radio.setChecked(True)
        
        self.type_group.addButton(self.single_row_radio, 0)
        self.type_group.addButton(self.blast_area_radio, 1)
        
        type_layout.addWidget(type_label)
        type_layout.addWidget(self.single_row_radio)
        type_layout.addWidget(self.blast_area_radio)
        type_layout.addStretch()
        
        layout.addLayout(type_layout)
        
        # 主要内容区域
        main_layout = QHBoxLayout()
        
        # 左侧示意图
        self.schematic_widget = HoleSchematicWidget()
        main_layout.addWidget(self.schematic_widget)
        
        # 右侧参数设置
        params_widget = self.create_parameters_widget()
        main_layout.addWidget(params_widget)
        
        layout.addLayout(main_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.calculate_btn = QPushButton("计算参数")
        self.calculate_btn.clicked.connect(self.calculate_parameters)
        
        self.next_btn = QPushButton("下一步")
        self.next_btn.clicked.connect(self.show_adjustment_dialog)
        self.next_btn.setEnabled(False)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.calculate_btn)
        button_layout.addWidget(self.next_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def create_parameters_widget(self):
        """创建参数设置组件"""
        widget = QWidget()
        widget.setMinimumWidth(400)
        layout = QVBoxLayout(widget)
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QFormLayout()
        
        # 爆破水平（只读）
        self.blast_level_label = QLabel(f"{self.blast_level:.1f} m")
        basic_layout.addRow("爆破水平:", self.blast_level_label)
        
        # 台阶高度（只读）
        self.step_height_label = QLabel(f"{self.step_height:.1f} m")
        basic_layout.addRow("台阶高度:", self.step_height_label)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 设计参数组
        design_group = QGroupBox("设计参数")
        design_layout = QFormLayout()
        
        # 炸药类型
        self.explosive_type_combo = QComboBox()
        design_layout.addRow("炸药类型:", self.explosive_type_combo)
        
        # 岩石类型
        self.rock_type_combo = QComboBox()
        design_layout.addRow("岩石类型:", self.rock_type_combo)
        
        # 工程类型
        self.project_type_combo = QComboBox()
        self.project_type_combo.addItems([
            "露天矿山剥离爆破", 
            "地下矿山开采爆破", 
            "基坑开挖爆破", 
            "土石方工程爆破"
        ])
        design_layout.addRow("工程类型:", self.project_type_combo)
        
        # 孔径
        self.diameter_spinbox = QDoubleSpinBox()
        self.diameter_spinbox.setRange(50.0, 300.0)
        self.diameter_spinbox.setValue(110.0)
        self.diameter_spinbox.setSuffix(" mm")
        self.diameter_spinbox.setDecimals(1)
        design_layout.addRow("孔径:", self.diameter_spinbox)
        
        # 设计孔深
        self.design_depth_spinbox = QDoubleSpinBox()
        self.design_depth_spinbox.setRange(1.0, 100.0)
        self.design_depth_spinbox.setValue(15.0)
        self.design_depth_spinbox.setSuffix(" m")
        self.design_depth_spinbox.setDecimals(1)
        design_layout.addRow("设计孔深:", self.design_depth_spinbox)
        
        # 回填长度
        self.backfill_length_spinbox = QDoubleSpinBox()
        self.backfill_length_spinbox.setRange(0.1, 20.0)
        self.backfill_length_spinbox.setValue(3.0)
        self.backfill_length_spinbox.setSuffix(" m")
        self.backfill_length_spinbox.setDecimals(1)
        design_layout.addRow("回填长度:", self.backfill_length_spinbox)
        
        # 布孔模式（仅爆区显示）
        self.layout_mode_combo = QComboBox()
        self.layout_mode_combo.addItems(["矩形布孔", "三角形布孔"])
        self.layout_mode_label = QLabel("布孔模式:")
        design_layout.addRow(self.layout_mode_label, self.layout_mode_combo)
        
        design_group.setLayout(design_layout)
        layout.addWidget(design_group)
        
        # 计算结果组
        result_group = QGroupBox("计算结果")
        result_layout = QFormLayout()
        
        # 孔距
        self.hole_spacing_label = QLabel("-- m")
        result_layout.addRow("孔距:", self.hole_spacing_label)
        
        # 排距
        self.row_spacing_label = QLabel("-- m")
        result_layout.addRow("排距:", self.row_spacing_label)
        
        # 炸药量
        self.explosive_amount_label = QLabel("-- kg")
        result_layout.addRow("炸药量:", self.explosive_amount_label)
        
        # 单位长度装药量
        self.unit_charge_label = QLabel("-- kg/m")
        result_layout.addRow("单位长度装药量:", self.unit_charge_label)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        layout.addStretch()
        return widget
    
    def connect_signals(self):
        """连接信号槽"""
        self.type_group.buttonClicked.connect(self.on_layout_type_changed)
        self.design_depth_spinbox.valueChanged.connect(self.update_schematic)
        self.backfill_length_spinbox.valueChanged.connect(self.update_schematic)
    
    def on_layout_type_changed(self, button):
        """布孔类型改变处理"""
        if button == self.single_row_radio:
            self.current_layout_type = "一排"
            self.layout_mode_label.hide()
            self.layout_mode_combo.hide()
        else:
            self.current_layout_type = "爆区"
            self.layout_mode_label.show()
            self.layout_mode_combo.show()
        
        # 重置计算结果
        self.reset_results()
    
    def load_parameters(self):
        """加载参数选项"""
        try:
            # 加载岩石类型
            self.load_rock_types()
            
            # 加载炸药类型
            self.load_explosive_types()
            
            # 初始化布孔模式显示
            self.on_layout_type_changed(self.single_row_radio)
            
        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载参数时出错: {e}")
    
    def load_rock_types(self):
        """加载岩石类型"""
        try:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            rock_file = os.path.join(data_dir, 'rock_parameters.json')
            
            if os.path.exists(rock_file):
                with open(rock_file, 'r', encoding='utf-8') as f:
                    rock_data = json.load(f)
                
                rock_names = [rock.get('岩石名称', '') for rock in rock_data if rock.get('岩石名称')]
                self.rock_type_combo.clear()
                self.rock_type_combo.addItems(rock_names)
            else:
                # 默认岩石类型
                self.rock_type_combo.clear()
                self.rock_type_combo.addItems(["花岗岩", "片麻岩", "石英岩", "片岩", "石灰岩"])
                
        except Exception as e:
            print(f"加载岩石类型时出错: {e}")
            self.rock_type_combo.clear()
            self.rock_type_combo.addItems(["花岗岩", "片麻岩", "石英岩", "片岩", "石灰岩"])
    
    def load_explosive_types(self):
        """加载炸药类型"""
        try:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            explosive_file = os.path.join(data_dir, 'explosive_parameters.json')
            
            if os.path.exists(explosive_file):
                with open(explosive_file, 'r', encoding='utf-8') as f:
                    explosive_data = json.load(f)
                
                explosive_names = [exp.get('炸药名称', '') for exp in explosive_data if exp.get('炸药名称')]
                self.explosive_type_combo.clear()
                self.explosive_type_combo.addItems(explosive_names)
            else:
                # 默认炸药类型
                self.explosive_type_combo.clear()
                self.explosive_type_combo.addItems(["ANFO", "乳化炸药", "TNT", "液氧炸药"])
                
        except Exception as e:
            print(f"加载炸药类型时出错: {e}")
            self.explosive_type_combo.clear()
            self.explosive_type_combo.addItems(["ANFO", "乳化炸药", "TNT", "液氧炸药"])
    
    def load_drilling_diameters(self):
        """加载打孔设备孔径"""
        # 此方法不再需要，因为孔径输入已改为SpinBox
        pass
    
    def update_schematic(self):
        """更新示意图"""
        design_depth = self.design_depth_spinbox.value()
        backfill_length = self.backfill_length_spinbox.value()
        
        # 计算最小填塞长度（简化计算）
        diameter = self.get_diameter_value()
        min_stemming = max(3.0 * diameter / 1000, 2.0)  # 简化计算
        
        self.schematic_widget.update_parameters(
            design_depth, backfill_length, min_stemming, self.step_height
        )
    
    def get_diameter_value(self):
        """获取孔径数值"""
        return self.diameter_spinbox.value()
    
    def get_project_type_key(self):
        """获取工程类型对应的键值"""
        type_map = {
            "露天矿山剥离爆破": "露天矿山",
            "地下矿山开采爆破": "地下矿山", 
            "基坑开挖爆破": "基坑开挖",
            "土石方工程爆破": "土石方工程"
        }
        return type_map.get(self.project_type_combo.currentText(), "露天矿山")
    
    def validate_parameters(self):
        """参数验证"""
        # 检查设计孔深是否大于台阶高度
        if self.design_depth_spinbox.value() <= self.step_height:
            QMessageBox.warning(self, "参数错误", 
                              f"设计孔深({self.design_depth_spinbox.value():.1f}m)应大于台阶高度({self.step_height:.1f}m)")
            return False
        
        # 检查回填长度是否小于设计孔深
        if self.backfill_length_spinbox.value() >= self.design_depth_spinbox.value():
            QMessageBox.warning(self, "参数错误", 
                              f"回填长度({self.backfill_length_spinbox.value():.1f}m)不能超过设计孔深({self.design_depth_spinbox.value():.1f}m)")
            return False
        
        return True
    
    def calculate_parameters(self):
        """计算参数"""
        if not self.validate_parameters():
            return
        
        try:
            # 构建计算参数
            # 对于一排布孔，layout_mode 默认为 '矩形布孔'
            layout_mode = self.layout_mode_combo.currentText() if self.current_layout_type == "爆区" else "矩形布孔"
            
            params = {
                'layout_type': self.current_layout_type,
                'layout_mode': layout_mode,
                'project_type': self.get_project_type_key(),
                'rock_type': self.rock_type_combo.currentText(),
                'explosive_type': self.explosive_type_combo.currentText(),
                'diameter': self.get_diameter_value(),
                'design_depth': self.design_depth_spinbox.value(),
                'backfill_length': self.backfill_length_spinbox.value(),
                'bench_height': self.step_height
            }
            
            # 执行计算
            self.calculation_results = self.calculator.calculate_comprehensive_design(params)
            
            # 检查回填长度是否大于最小填塞长度
            min_stemming = self.calculation_results.get('min_stemming', 0)
            if self.backfill_length_spinbox.value() < min_stemming:
                QMessageBox.warning(self, "参数警告", 
                                  f"回填长度({self.backfill_length_spinbox.value():.1f}m)小于最小填塞长度({min_stemming:.2f}m)，建议调整")
            
            # 更新结果显示
            self.update_results_display()
            
            # 更新示意图
            self.schematic_widget.update_parameters(
                params['design_depth'], 
                params['backfill_length'], 
                min_stemming, 
                self.step_height
            )
            
            # 启用下一步按钮
            self.next_btn.setEnabled(True)
            
            QMessageBox.information(self, "计算完成", "参数计算完成，可以进行下一步操作")
            
        except Exception as e:
            QMessageBox.critical(self, "计算错误", f"参数计算失败: {e}")
    
    def update_results_display(self):
        """更新结果显示"""
        if not self.calculation_results:
            return
        
        self.hole_spacing_label.setText(f"{self.calculation_results.get('hole_spacing', 0):.2f} m")
        self.row_spacing_label.setText(f"{self.calculation_results.get('row_spacing', 0):.2f} m")
        self.explosive_amount_label.setText(f"{self.calculation_results.get('explosive_amount', 0):.2f} kg")
        self.unit_charge_label.setText(f"{self.calculation_results.get('unit_charge', 0):.2f} kg/m")
    
    def reset_results(self):
        """重置计算结果"""
        self.calculation_results = {}
        self.hole_spacing_label.setText("-- m")
        self.row_spacing_label.setText("-- m")
        self.explosive_amount_label.setText("-- kg")
        self.unit_charge_label.setText("-- kg/m")
        self.next_btn.setEnabled(False)
    
    def show_adjustment_dialog(self):
        """显示参数调整对话框"""
        if not self.calculation_results:
            QMessageBox.warning(self, "警告", "请先计算参数")
            return
        
        from .layout_adjustment_dialog import LayoutAdjustmentDialog
        
        dialog = LayoutAdjustmentDialog(
            self.current_layout_type,
            self.calculation_results,
            self
        )
        
        if dialog.exec_() == QDialog.Accepted:
            # 获取调整后的参数
            adjusted_params = dialog.get_adjusted_parameters()
            
            # 发送布孔信号
            layout_data = {
                'input_params': self.get_input_parameters(),
                'results': self.calculation_results,
                'adjusted_params': adjusted_params,
                'layout_type': self.current_layout_type
            }
            
            self.layout_calculated.emit(layout_data)
            self.close()
    
    def get_input_parameters(self):
        """获取输入参数"""
        # 对于一排布孔，layout_mode 默认为 '矩形布孔'
        layout_mode = self.layout_mode_combo.currentText() if self.current_layout_type == "爆区" else "矩形布孔"
        
        return {
            'layout_type': self.current_layout_type,
            'layout_mode': layout_mode,
            'project_type': self.get_project_type_key(),
            'rock_type': self.rock_type_combo.currentText(),
            'explosive_type': self.explosive_type_combo.currentText(),
            'diameter': self.get_diameter_value(),
            'design_depth': self.design_depth_spinbox.value(),
            'backfill_length': self.backfill_length_spinbox.value(),
            'bench_height': self.step_height,
            'blast_level': self.blast_level
        }

if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    dialog = IntelligentLayoutDialog()
    dialog.show()
    sys.exit(app.exec_()) 