# -*- coding: utf-8 -*-
"""
PyInstaller 打包脚本
用于将燃气能量岩石破碎系统打包成单个可执行文件。

用法:
在项目根目录下运行:
python build.py
"""

import os
import platform
import shutil
import subprocess

# --- 配置 ---
APP_NAME = "GNRB"
ENTRY_POINT = "main.py"
ICON_PATH = "image/logo.ico"  # 将用于生成程序的图标
DIST_PATH = "dist"
BUILD_PATH = "build"

def main():
    """执行打包过程"""
    print("开始打包过程...")

    # 确定数据文件路径的分隔符
    # Windows: ;
    # macOS/Linux: :
    path_separator = ';' if platform.system() == 'Windows' else ':'

    # 构建PyInstaller命令
    command = [
        'pyinstaller',
        '--name', APP_NAME,
        '--onefile',
        '--windowed',
        '--clean',  # 清理旧的打包文件
    ]

    # 添加图标
    if os.path.exists(ICON_PATH):
        command.extend(['--icon', ICON_PATH])
    else:
        print(f"警告: 图标文件未找到于 '{ICON_PATH}'，将不使用图标。")

    # 添加data目录
    # --add-data "源路径(分隔符)目标路径"
    command.extend(['--add-data', f'data{path_separator}data'])
    command.extend(['--add-data', f'image{path_separator}image']) # 添加image目录


    # 添加入口点
    command.append(ENTRY_POINT)

    # 打印将要执行的命令
    print("-" * 50)
    print("将要执行的命令:")
    print(" ".join(command))
    print("-" * 50)

    # 执行命令
    try:
        # 使用subprocess.run并显示实时输出
        process = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, encoding='utf-8', errors='replace')
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
                
        if process.poll() != 0:
             raise subprocess.CalledProcessError(process.poll(), " ".join(command))

        print("\n打包成功!")
        print(f"可执行文件位于: {os.path.join(DIST_PATH, f'{APP_NAME}.exe')}")

    except subprocess.CalledProcessError as e:
        print("\n打包失败!")
        print(f"返回码: {e.returncode}")
        return
    except FileNotFoundError:
        print("\n打包失败! 'pyinstaller' 命令未找到。")
        print("请确保您已经安装了PyInstaller: pip install pyinstaller")
        return

    # 清理临时文件
    print("\n清理临时文件...")
    try:
        if os.path.exists(BUILD_PATH):
            shutil.rmtree(BUILD_PATH)
        # 移除 .spec 文件
        spec_file = f"{APP_NAME}.spec"
        if os.path.exists(spec_file):
            os.remove(spec_file)
        print("清理完成。")
    except Exception as e:
        print(f"清理临时文件时出错: {e}")

if __name__ == "__main__":
    main() 