# -*- coding: utf-8 -*-
"""
工具栏组件
包含常用操作的快捷按钮
"""

from PyQt5.QtWidgets import QToolBar, QPushButton, QAction, QWidget
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QPen

class ToolbarWidget(QToolBar):
    """工具栏组件"""
    
    # 信号定义
    undo_requested = pyqtSignal()
    redo_requested = pyqtSignal()
    # view_requested = pyqtSignal()
    move_requested = pyqtSignal()
    # zoom_requested = pyqtSignal()
    zoom_in_requested = pyqtSignal()
    zoom_out_requested = pyqtSignal()
    fit_view_requested = pyqtSignal()
    print_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__("常用工具", parent)
        self.setMovable(False)
        self.setFloatable(False)
        self.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        self.setup_actions()
    
    def setup_actions(self):
        """设置工具栏操作"""
        
        # 撤销
        self.undo_action = QAction("撤销", self)
        self.undo_action.setToolTip("撤销上一步操作 (Ctrl+Z)")
        self.undo_action.setIcon(self.create_icon("undo"))
        self.undo_action.triggered.connect(self.undo_requested.emit)
        self.undo_action.setEnabled(False)  # 初始状态不可用
        self.addAction(self.undo_action)
        
        # 重做
        self.redo_action = QAction("重做", self)
        self.redo_action.setToolTip("重做上一步撤销操作 (Ctrl+Y)")
        self.redo_action.setIcon(self.create_icon("redo"))
        self.redo_action.triggered.connect(self.redo_requested.emit)
        self.redo_action.setEnabled(False)  # 初始状态不可用
        self.addAction(self.redo_action)
        
        self.addSeparator()
        
        # # 查看配置
        # view_action = QAction("查看", self)
        # view_action.setToolTip("配置显示项目")
        # view_action.setIcon(self.create_icon("view"))
        # view_action.triggered.connect(self.view_requested.emit)
        # self.addAction(view_action)
        
        # self.addSeparator()
        
        # 移动
        move_action = QAction("移动", self)
        move_action.setToolTip("激活移动模式")
        move_action.setIcon(self.create_icon("move"))
        move_action.triggered.connect(self.move_requested.emit)
        self.addAction(move_action)
        
        # # 缩放
        # zoom_action = QAction("缩放", self)
        # zoom_action.setToolTip("激活缩放模式")
        # zoom_action.setIcon(self.create_icon("zoom"))
        # zoom_action.triggered.connect(self.zoom_requested.emit)
        # self.addAction(zoom_action)
        
        self.addSeparator()
        
        # 放大
        zoom_in_action = QAction("放大", self)
        zoom_in_action.setToolTip("放大视图")
        zoom_in_action.setIcon(self.create_icon("zoom_in"))
        zoom_in_action.triggered.connect(self.zoom_in_requested.emit)
        self.addAction(zoom_in_action)
        
        # 缩小
        zoom_out_action = QAction("缩小", self)
        zoom_out_action.setToolTip("缩小视图")
        zoom_out_action.setIcon(self.create_icon("zoom_out"))
        zoom_out_action.triggered.connect(self.zoom_out_requested.emit)
        self.addAction(zoom_out_action)
        
        # 全图
        fit_view_action = QAction("全图", self)
        fit_view_action.setToolTip("适应全图显示")
        fit_view_action.setIcon(self.create_icon("fit_view"))
        fit_view_action.triggered.connect(self.fit_view_requested.emit)
        self.addAction(fit_view_action)
        
        self.addSeparator()
        
        # 打印
        print_action = QAction("打印", self)
        print_action.setToolTip("打印图形或表格")
        print_action.setIcon(self.create_icon("print"))
        print_action.triggered.connect(self.print_requested.emit)
        self.addAction(print_action)
    
    def create_icon(self, icon_type):
        """创建简单的图标"""
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        pen = QPen(Qt.black, 2)
        painter.setPen(pen)
        
        # if icon_type == "view":
        #     # 绘制查看图标 (眼睛)
        #     painter.drawEllipse(8, 10, 8, 4)
        #     painter.fillRect(11, 11, 2, 2, Qt.black)
            
        if icon_type == "move":
            # 绘制移动图标 (四向箭头)
            painter.drawLine(12, 4, 12, 20)
            painter.drawLine(4, 12, 20, 12)
            painter.drawLine(8, 8, 12, 4)
            painter.drawLine(16, 8, 12, 4)
            painter.drawLine(8, 16, 12, 20)
            painter.drawLine(16, 16, 12, 20)
            painter.drawLine(4, 8, 4, 16)
            painter.drawLine(20, 8, 20, 16)
            
        # elif icon_type == "zoom":
        #     # 绘制缩放图标 (放大镜)
        #     painter.drawEllipse(4, 4, 12, 12)
        #     painter.drawLine(14, 14, 20, 20)
            
        elif icon_type == "zoom_in":
            # 绘制放大图标 (放大镜+)
            painter.drawEllipse(4, 4, 12, 12)
            painter.drawLine(14, 14, 20, 20)
            painter.drawLine(10, 7, 10, 13)
            painter.drawLine(7, 10, 13, 10)
            
        elif icon_type == "zoom_out":
            # 绘制缩小图标 (放大镜-)
            painter.drawEllipse(4, 4, 12, 12)
            painter.drawLine(14, 14, 20, 20)
            painter.drawLine(7, 10, 13, 10)
            
        elif icon_type == "fit_view":
            # 绘制全图图标 (四角框)
            painter.drawRect(4, 4, 16, 16)
            painter.drawLine(4, 4, 8, 8)
            painter.drawLine(20, 4, 16, 8)
            painter.drawLine(4, 20, 8, 16)
            painter.drawLine(20, 20, 16, 16)
            
        elif icon_type == "print":
            # 绘制打印图标 (打印机)
            painter.drawRect(6, 8, 12, 8)
            painter.drawRect(8, 4, 8, 4)
            painter.drawRect(8, 16, 8, 4)
            
        elif icon_type == "undo":
            # 绘制撤销图标 (左弯曲箭头)
            painter.drawArc(6, 6, 12, 12, 0, 180*16)  # 半圆弧
            painter.drawLine(6, 12, 10, 8)  # 箭头上
            painter.drawLine(6, 12, 10, 16)  # 箭头下
            
        elif icon_type == "redo":
            # 绘制重做图标 (右弯曲箭头)
            painter.drawArc(6, 6, 12, 12, 0, 180*16)  # 半圆弧
            painter.drawLine(18, 12, 14, 8)  # 箭头上
            painter.drawLine(18, 12, 14, 16)  # 箭头下
            
        painter.end()
        return QIcon(pixmap)
    
    def update_undo_redo_status(self, can_undo, can_redo):
        """更新撤销/重做按钮的状态"""
        self.undo_action.setEnabled(can_undo)
        self.redo_action.setEnabled(can_redo)
 