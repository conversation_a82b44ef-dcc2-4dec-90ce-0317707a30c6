# -*- coding: utf-8 -*-
"""
主窗口类
包含主要的用户界面组件和布局
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QSplitter, QTreeWidget, QTreeWidgetItem, QTextEdit,
                            QMenuBar, QMenu, QAction, QToolBar, QStatusBar,
                            QMessageBox, QFileDialog, QLabel, QFrame, QDialog,
                            QCheckBox, QPushButton, QGroupBox, QComboBox, QFormLayout)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
import math
import os

from .components.menu_tree import MenuTreeWidget
from .components.design_area import DesignArea
from .components.toolbar_widget import ToolbarWidget
from .dialogs.parameter_dialogs import (RockParameterDialog, ExplosiveParameterDialog, 
                                      DrillingEquipmentDialog)
from .dialogs.file_dialogs import (NewDesignDialog, DataImportDialog, PrintSettingsDialog)
from .dialogs.hole_design_dialogs import AssignDrillingEquipmentDialog
from .utils.resource_helper import resource_path

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("气能破岩参数设计云平台")
        self.setGeometry(100, 100, 1400, 900)
        
        # 设置应用程序图标
        self.set_app_icon()
        
        # 当前打开的文件路径
        self.current_file_path = None
        
        # 设计参数
        self.design_name = "未命名设计"
        self.blast_level = 125.0  # 设置为125.0以匹配测试数据的Z坐标
        self.step_height = 15.0
        
        # 当前设置参数
        self.current_hole_type = "主炮孔"
        self.current_hole_diameter = 110.0
        self.current_rock_type = "花岗岩"
        self.current_explosive_type = "2号岩石铵梯炸药"
        self.current_charge_structure = "连续装药"  # 添加当前装药结构
        self.current_hole_spacing = 3.5
        self.current_row_spacing = 4.0
        
        # 参数数据缓存
        self.rock_types = ["花岗岩", "石灰岩", "砂岩", "页岩", "玄武岩", "大理岩", "片麻岩", "辉绿岩", "安山岩", "流纹岩"]
        self.explosive_types = ["2号岩石铵梯炸药", "3号岩石铵梯炸药", "4号岩石铵梯炸药", "5号岩石铵梯炸药", "胶质炸药", "乳化炸药", "PETN炸药"]
        self.drilling_equipment = []
        self.explosive_data = [] # 新增，用于存储完整的炸药信息
        
        # 加载默认参数
        self._load_default_drilling_equipment()
        self._load_explosive_data()
        
        # 加载保存的参数
        self.load_global_parameters()
        
        # 初始化界面
        self.init_ui()
        
        # 连接信号槽
        self.connect_signals()
        
        # 初始化视图配置
        self.view_config = {
            'design_name': True,      # 设计名称
            'hole_id': True,          # 孔号
            'hole_depth': True,       # 孔深
            'spacing': True,          # 间距排距
            'work_surface': True,     # 作业面
            'boundary': True,         # 边界线
            'text': True,             # 文本
            'explosive_amount': True, # 炸药量
            'hole_diameter': True,    # 孔径
            'rock_type': True,        # 岩石
            'explosive_type': True,   # 炸药
            'hole_type': True,        # 孔类型
            'coordinates': True,      # 坐标
            'grid': True,             # 网格
            'connection_lines': True, # 连接线
        }
        

    
    def set_app_icon(self):
        """设置应用程序图标"""
        # 为了在开发和打包后都能找到图标，使用相对路径
        # 这个路径是相对于项目根目录的
        icon_path = resource_path("image/logo.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        else:
            # 如果图标不存在，可以动态创建一个或打印提示
            print(f"警告: 应用程序图标未找到于 '{icon_path}'")
            # 您也可以在这里放一个后备的动态创建图标的逻辑
            pass
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 编辑菜单
        edit_menu = menubar.addMenu('编辑(&E)')
        
        self.undo_action = QAction('撤销(&U)', self)
        self.undo_action.setShortcut('Ctrl+Z')
        self.undo_action.triggered.connect(self.undo_operation)
        self.undo_action.setEnabled(False)
        edit_menu.addAction(self.undo_action)
        
        self.redo_action = QAction('重做(&R)', self)
        self.redo_action.setShortcut('Ctrl+Y')
        self.redo_action.triggered.connect(self.redo_operation)
        self.redo_action.setEnabled(False)
        edit_menu.addAction(self.redo_action)
        
        edit_menu.addSeparator()
        
        clear_screen_action = QAction('清空屏幕(&C)', self)
        clear_screen_action.triggered.connect(self.clear_screen)
        edit_menu.addAction(clear_screen_action)
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        new_action = QAction('新建(&N)', self)
        new_action.setShortcut('Ctrl+N')
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        open_action = QAction('打开(&O)', self)
        open_action.setShortcut('Ctrl+O')
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        save_action = QAction('保存(&S)', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_file)
        file_menu.addAction(save_action)
        
        save_as_action = QAction('另存为(&A)', self)
        save_as_action.setShortcut('Ctrl+Shift+S')
        save_as_action.triggered.connect(self.save_as_file)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        import_action = QAction('数据导入(&I)', self)
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        print_action = QAction('图形打印(&P)', self)
        print_action.setShortcut('Ctrl+P')
        print_action.triggered.connect(self.print_graphics)
        file_menu.addAction(print_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Alt+F4')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 参数菜单
        param_menu = menubar.addMenu('参数(&P)')
        
        rock_param_action = QAction('岩石参数(&R)', self)
        rock_param_action.triggered.connect(self.open_rock_parameters)
        param_menu.addAction(rock_param_action)
        
        explosive_param_action = QAction('炸药参数(&E)', self)
        explosive_param_action.triggered.connect(self.open_explosive_parameters)
        param_menu.addAction(explosive_param_action)
        
        drilling_param_action = QAction('打孔设备(&D)', self)
        drilling_param_action.triggered.connect(self.open_drilling_equipment)
        param_menu.addAction(drilling_param_action)
        
        # 设计菜单
        design_menu = menubar.addMenu('设计(&D)')
        
        preliminary_menu = design_menu.addMenu('初步设计(&P)')
        preliminary_menu.addAction('炮孔(&H)', self.design_holes)
        
        detailed_menu = design_menu.addMenu('精细化设计(&D)')
        
        diameter_action = QAction('孔径', self)
        diameter_action.triggered.connect(self.set_drilling_equipment)
        detailed_menu.addAction(diameter_action)
        
        rock_action = QAction('岩石', self)
        rock_action.triggered.connect(self.start_rock_type_setting)
        detailed_menu.addAction(rock_action)
        
        detailed_menu.addSeparator()
        detailed_menu.addAction('炸药(&E)', self.set_explosive_type)
        detailed_menu.addAction('间排距(&S)', self.set_spacing)
        detailed_menu.addAction('连接成排(&C)', self.connect_rows)
        detailed_menu.addAction('编辑炸药量(&A)', self.edit_explosive_amount)
        detailed_menu.addAction('作业面(&W)', self.draw_work_surface)
        detailed_menu.addAction('边界线(&B)', self.draw_boundary)
        detailed_menu.addAction('文本(&T)', self.add_text)
        
        # 输出菜单
        output_menu = menubar.addMenu('输出(&O)')
        output_menu.addAction('爆破网络图(&N)', self.generate_network_diagram)
        output_menu.addAction('爆破参数表(&P)', self.generate_parameter_table)
        
        # 本地文件菜单
        local_menu = menubar.addMenu('本地文件(&L)')
        local_menu.addAction('文件管理器(&M)', self.open_file_manager)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_widget = ToolbarWidget(self)
        self.addToolBar(Qt.TopToolBarArea, self.toolbar_widget)
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧菜单树
        self.menu_tree = MenuTreeWidget()
        self.menu_tree.setMaximumWidth(250)
        self.menu_tree.setMinimumWidth(200)
        splitter.addWidget(self.menu_tree)
        
        # 创建右侧设计区域
        self.design_area = DesignArea()
        self.design_area.set_main_window(self)  # 设置主窗口引用
        splitter.addWidget(self.design_area)
        
        # 设置分割器比例
        splitter.setSizes([250, 1150])
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 添加状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 添加坐标标签
        self.coord_label = QLabel("坐标: (0, 0)")
        self.status_bar.addPermanentWidget(self.coord_label)
        
        # 添加缩放比例标签
        self.zoom_label = QLabel("缩放: 100%")
        self.status_bar.addPermanentWidget(self.zoom_label)
    
    def connect_signals(self):
        """连接信号槽"""
        # 连接菜单树信号
        self.menu_tree.item_selected.connect(self.on_menu_item_selected)
        
        # 连接工具栏信号
        self.toolbar_widget.undo_requested.connect(self.undo_operation)
        self.toolbar_widget.redo_requested.connect(self.redo_operation)
        # self.toolbar_widget.view_requested.connect(self.show_view_config_dialog)
        self.toolbar_widget.move_requested.connect(self.toggle_move_mode)
        # self.toolbar_widget.zoom_requested.connect(self.toggle_zoom_mode)
        self.toolbar_widget.zoom_in_requested.connect(self.zoom_in)
        self.toolbar_widget.zoom_out_requested.connect(self.zoom_out)
        self.toolbar_widget.fit_view_requested.connect(self.fit_view)
        self.toolbar_widget.print_requested.connect(self.print_graphics)
        
        # 连接设计区域信号
        self.design_area.coordinates_changed.connect(self.update_coordinates)
        self.design_area.zoom_changed.connect(self.update_zoom)
        
        # 初始化撤销/重做按钮状态
        self.update_undo_redo_status()
    
    def update_status(self, message):
        """更新状态栏消息"""
        self.status_label.setText(message)
    
    def update_coordinates(self, x, y):
        """更新坐标显示"""
        self.coord_label.setText(f"坐标: ({x:.1f}, {y:.1f})")
    
    def update_zoom(self, zoom_percent):
        """更新缩放比例显示"""
        self.zoom_label.setText(f"缩放: {zoom_percent:.0f}%")
    
    def undo_operation(self):
        """撤销操作"""
        if hasattr(self.design_area, 'undo'):
            self.design_area.undo()
            self.update_undo_redo_status()
            self.update_status("已撤销上一步操作")
    
    def redo_operation(self):
        """重做操作"""  
        if hasattr(self.design_area, 'redo'):
            self.design_area.redo()
            self.update_undo_redo_status()
            self.update_status("已重做上一步操作")
    
    def update_undo_redo_status(self):
        """更新撤销/重做按钮的状态"""
        if hasattr(self.design_area, 'history') and hasattr(self.design_area, 'history_index'):
            can_undo = self.design_area.history_index > 0
            can_redo = self.design_area.history_index < len(self.design_area.history) - 1
            
            # 更新菜单项状态
            self.undo_action.setEnabled(can_undo)
            self.redo_action.setEnabled(can_redo)
            
            # 更新工具栏按钮状态
            if hasattr(self.toolbar_widget, 'update_undo_redo_status'):
                self.toolbar_widget.update_undo_redo_status(can_undo, can_redo)
            
            # 更新菜单树状态
            if hasattr(self.menu_tree, 'update_undo_redo_status'):
                self.menu_tree.update_undo_redo_status(can_undo, can_redo)
    
    # 菜单项点击事件处理
    def on_menu_item_selected(self, item_text):
        """处理菜单树项目选择事件"""
        self.update_status(f"选择了: {item_text}")
        
        # 根据选择的菜单项执行相应操作
        # 编辑菜单
        if item_text == "撤销":
            self.undo_operation()
        elif item_text == "重做":
            self.redo_operation()
        elif item_text == "清空屏幕":
            self.clear_screen()
        
        # 文件菜单
        elif item_text == "新建":
            self.new_file()
        elif item_text == "打开":
            self.open_file()
        elif item_text == "保存":
            self.save_file()
        elif item_text == "另存为":
            self.save_as_file()
        elif item_text == "数据导入":
            self.import_data()
        elif item_text == "图形打印":
            self.print_graphics()
        
        # 参数菜单
        elif item_text == "岩石参数":
            self.open_rock_parameters()
        elif item_text == "炸药参数":
            self.open_explosive_parameters()
        elif item_text == "打孔设备":
            self.open_drilling_equipment()
        
        # 设计菜单 - 初步设计
        elif item_text == "炮孔":
            self.design_holes()
        elif item_text == "孔类型设置":
            self.design_area.start_hole_type_setting_mode()
        
        # 炮孔初步设计
        elif item_text == "增加炮孔":
            self.start_add_hole()
        elif item_text == "通过坐标添加":
            self.add_hole_by_coordinate()
        elif item_text == "删除炮孔":
            self.start_delete_hole()
        elif item_text == "清空所有炮孔":
            self.clear_all_holes()
        elif item_text == "移动炮孔":
            self.start_move_hole()
        elif item_text == "修改坐标":
            self.modify_hole_coordinates()
        elif item_text == "修改数据":
            self.modify_hole_data()
        elif item_text == "修改AVV":
            self.modify_hole_avv()
        elif item_text == "智能布孔":
            self.show_intelligent_layout()
        elif item_text == "退出操作模式":
            self.exit_hole_operation()
        
        # 设计菜单 - 精细化设计
        elif item_text == "孔径":
            self.set_drilling_equipment()  # 调用只对选中炮孔生效的方法
        elif item_text == "岩石":
            print("菜单项'岩石'被点击，调用 start_rock_type_setting()")
            self.start_rock_type_setting()  # 调用精细化设计的岩石设置方法
        elif item_text == "炸药":
            self.set_explosive_type()
        elif item_text == "间排距":
            self.set_spacing()
        elif item_text == "连接成排":
            self.connect_rows()
        elif item_text == "编辑炸药量":
            self.edit_explosive_amount()
        elif item_text == "作业面":
            self.draw_work_surface()
        elif item_text == "边界线":
            self.draw_boundary()
        elif item_text == "文本":
            self.add_text()
        
        # 输出菜单
        elif item_text == "爆破网络图":
            self.generate_network_diagram()
        elif item_text == "爆破参数表":
            self.generate_parameter_table()
        
        # 本地文件菜单
        elif item_text == "文件管理器":
            self.open_file_manager()
        
        else:
            self.update_status(f"功能 '{item_text}' 尚未实现")
    
    def clear_screen(self):
        """清空屏幕上所有元素"""
        self.design_area.clear_screen()
    
    # 文件操作方法
    def new_file(self):
        """新建文件"""
        dialog = NewDesignDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.update_status(f"创建新设计: {dialog.design_name}")
            self.design_area.clear_design()
            self.current_file_path = None
            
            # 保存设计参数
            self.design_name = dialog.design_name
            self.blast_level = dialog.blast_level
            self.step_height = dialog.step_height
            
            title = f"气能破岩参数设计云平台 - {dialog.design_name} [新建文件]"
            self.setWindowTitle(title)
            
            QMessageBox.information(self, "新建成功", 
                                  f"设计名称: {dialog.design_name}\n"
                                  f"爆破水平: {dialog.blast_level}m\n"
                                  f"台阶段高: {dialog.step_height}m")
    
    def open_file(self):
        """打开文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开设计文件", "", "设计文件 (*.gerb);;JSON文件 (*.json);;所有文件 (*)")
        if file_path:
            try:
                self.update_status(f"正在打开文件: {file_path}")
                
                import json
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.update_status("文件读取成功，正在解析数据...")
                
                # 检查文件格式
                if not isinstance(data, dict):
                    QMessageBox.warning(self, "格式错误", 
                                      "这不是有效的设计文件格式。\n"
                                      "设计文件应该是包含design_info、holes等字段的JSON对象。")
                    return
                
                # 清空当前设计
                self.design_area.clear_design()
                
                # 加载设计参数
                if 'design_info' in data:
                    design_info = data['design_info']
                    self.design_name = design_info.get('name', '未命名设计')
                    self.blast_level = design_info.get('blast_level', 0.0)
                    self.step_height = design_info.get('step_height', 15.0)
                    self.update_status(f"加载设计参数: {self.design_name}")
                else:
                    self.design_name = "导入的设计"
                    self.blast_level = 0.0
                    self.step_height = 15.0
                    self.update_status("未找到设计参数，使用默认值")
                
                hole_count = 0
                boundary_count = 0
                
                # 加载炮孔数据
                if 'holes' in data:
                    for hole_data in data['holes']:
                        x = hole_data.get('x', 0)
                        y = hole_data.get('y', 0)
                        diameter = hole_data.get('diameter', 110)
                        hole = self.design_area.add_blast_hole(x, y, diameter)
                        hole.hole_id = hole_data.get('hole_id', f'H{hole_count+1:03d}')
                        hole.depth = hole_data.get('depth', 15)
                        hole.rock_type = hole_data.get('rock_type', '花岗岩')
                        hole.explosive_type = hole_data.get('explosive_type', '2号岩石铵梯炸药')
                        hole.explosive_amount = hole_data.get('explosive_amount', 25.0)
                        hole.charge_structure = hole_data.get('charge_structure', '连续装药')
                        # 更新所有显示
                        hole.update_text_label()
                        hole.update_rock_display()
                        hole.update_explosive_display()
                        hole_count += 1
                    self.update_status(f"加载了 {hole_count} 个炮孔")
                
                # 加载边界线数据
                if 'boundaries' in data:
                    for boundary in data['boundaries']:
                        if boundary.get('type') == 'line' and 'start' in boundary and 'end' in boundary:
                            # 旧格式的直线边界
                            x1, y1 = boundary['start']
                            x2, y2 = boundary['end']
                            self.design_area.add_boundary_line(x1, y1, x2, y2)
                            boundary_count += 1
                        elif boundary.get('type') == 'polygon' and 'points' in boundary:
                            # 新格式的多边形边界
                            from PyQt5.QtCore import QPointF
                            points = [QPointF(x, y) for x, y in boundary['points']]
                            from src.components.design_area import BoundaryPolygonItem
                            boundary_item = BoundaryPolygonItem(points)
                            boundary_item.item_type = "boundary"
                            self.design_area.graphics_scene.addItem(boundary_item)
                            boundary_count += 1
                        elif 'start' in boundary and 'end' in boundary:
                            # 兼容旧格式（没有type字段）
                            x1, y1 = boundary['start']
                            x2, y2 = boundary['end']
                            self.design_area.add_boundary_line(x1, y1, x2, y2)
                            boundary_count += 1
                    self.update_status(f"加载了 {boundary_count} 条边界线")
                
                # 加载作业面数据
                work_surface_count = 0
                if 'work_surfaces' in data:
                    for work_surface in data['work_surfaces']:
                        if 'points' in work_surface:
                            from PyQt5.QtCore import QPointF
                            points = [QPointF(x, y) for x, y in work_surface['points']]
                            from src.components.design_area import WorkSurfaceItem
                            work_surface_item = WorkSurfaceItem(points)
                            work_surface_item.item_type = "work_surface"
                            self.design_area.graphics_scene.addItem(work_surface_item)
                            work_surface_count += 1
                    self.update_status(f"加载了 {work_surface_count} 个作业面")
                
                # 加载文本数据
                text_count = 0
                if 'texts' in data:
                    for text_data in data['texts']:
                        x = text_data.get('x', 0)
                        y = text_data.get('y', 0)
                        text = text_data.get('text', '文本')
                        self.design_area.add_text(x, y, text)
                        text_count += 1
                    self.update_status(f"加载了 {text_count} 个文本标注")
                
                # 加载参考点数据
                ref_count = 0
                if 'references' in data:
                    for ref_data in data['references']:
                        x = ref_data.get('x', 0)
                        y = ref_data.get('y', 0)
                        self.design_area.add_reference_point(x, y)
                        ref_count += 1
                    self.update_status(f"加载了 {ref_count} 个参考点")
                
                self.current_file_path = file_path
                self.update_status(f"文件打开成功: {file_path}")
                self.setWindowTitle(f"气能破岩参数设计云平台 - {self.design_name}")
                self.design_area.update_table_data()
                self.fit_view()
                
                # 保存加载后的初始状态用于撤销重做
                self.design_area.save_state()
                
                QMessageBox.information(self, "打开成功", 
                                      f"成功打开设计文件: {self.design_name}\n"
                                      f"炮孔数量: {hole_count}\n"
                                      f"边界线数量: {boundary_count}\n"
                                      f"作业面数量: {work_surface_count}\n"
                                      f"文本标注: {text_count}\n"
                                      f"参考点: {ref_count}")
                
            except json.JSONDecodeError as e:
                QMessageBox.critical(self, "JSON格式错误", 
                                   f"文件不是有效的JSON格式:\n{str(e)}")
            except FileNotFoundError:
                QMessageBox.critical(self, "文件不存在", "指定的文件不存在")
            except PermissionError:
                QMessageBox.critical(self, "权限错误", "没有权限读取该文件")
            except Exception as e:
                QMessageBox.critical(self, "打开失败", 
                                   f"无法打开文件:\n{str(e)}\n\n"
                                   f"请确保文件是有效的设计文件格式")
    
    def save_file(self):
        """保存文件"""
        if self.current_file_path:
            self._save_to_file(self.current_file_path)
        else:
            self.save_as_file()
    
    def save_as_file(self):
        """另存为文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "另存为", "", "设计文件 (*.gerb);;JSON文件 (*.json);;所有文件 (*)")
        if file_path:
            self._save_to_file(file_path)
            self.current_file_path = file_path
    
    def _save_to_file(self, file_path):
        """保存文件到指定路径"""
        try:
            import json
            
            # 收集设计数据
            data = {
                'design_info': {
                    'name': self.design_name,
                    'blast_level': self.blast_level,
                    'step_height': self.step_height,
                    'created_time': str(__import__('datetime').datetime.now())
                },
                'holes': [],
                'boundaries': [],
                'texts': [],
                'references': []
            }
            
            # 收集炮孔数据
            holes = self.design_area.get_blast_holes()
            for hole in holes:
                hole_data = {
                    'hole_id': getattr(hole, 'hole_id', ''),
                    'x': hole.pos().x(),
                    'y': hole.pos().y(),
                    'diameter': getattr(hole, 'diameter_value', 12),
                    'depth': getattr(hole, 'depth', 15),
                    'rock_type': getattr(hole, 'rock_type', ''),
                    'explosive_type': getattr(hole, 'explosive_type', ''),
                    'explosive_amount': getattr(hole, 'explosive_amount', 0),
                    'charge_structure': getattr(hole, 'charge_structure', '连续装药')
                }
                data['holes'].append(hole_data)
            
            # 收集其他图形元素数据
            for item in self.design_area.graphics_scene.items():
                if hasattr(item, 'item_type'):
                    if item.item_type == 'boundary_line':
                        line = item.line()
                        boundary_data = {
                            'type': 'line',
                            'start': [line.x1(), line.y1()],
                            'end': [line.x2(), line.y2()]
                        }
                        data['boundaries'].append(boundary_data)
                    elif item.item_type == 'boundary':
                        # 新的闭合边界线
                        boundary_data = {
                            'type': 'polygon',
                            'points': [[p.x(), p.y()] for p in item.points]
                        }
                        data['boundaries'].append(boundary_data)
                    elif item.item_type == 'work_surface':
                        # 作业面
                        work_surface_data = {
                            'points': [[p.x(), p.y()] for p in item.points]
                        }
                        data.setdefault('work_surfaces', []).append(work_surface_data)
                    elif item.item_type == 'text_annotation':
                        # 文本标注
                        text_data = {
                            'x': item.pos().x(),
                            'y': item.pos().y(),
                            'text': item.toPlainText()
                        }
                        data['texts'].append(text_data)
                    elif item.item_type == 'reference_point':
                        # 参考点
                        ref_data = {
                            'x': item.pos().x() + 3,  # 调整中心点
                            'y': item.pos().y() + 3,
                            'label': '参考点'
                        }
                        data['references'].append(ref_data)
                elif hasattr(item, 'toPlainText') and not hasattr(item, 'item_type'):  # 旧版本文本项
                    text_data = {
                        'x': item.pos().x(),
                        'y': item.pos().y(),
                        'text': item.toPlainText()
                    }
                    data['texts'].append(text_data)
                elif hasattr(item, 'rect') and item.rect().width() == 6 and not hasattr(item, 'item_type'):  # 旧版本参考点
                    ref_data = {
                        'x': item.pos().x() + 3,  # 调整中心点
                        'y': item.pos().y() + 3,
                        'label': '参考点'
                    }
                    data['references'].append(ref_data)
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.update_status(f"保存文件: {file_path}")
            self.setWindowTitle(f"气能破岩参数设计云平台 - {self.design_name}")
            QMessageBox.information(self, "保存成功", f"设计文件已保存: {file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"无法保存文件: {str(e)}")
    
    def import_data(self):
        """数据导入"""
        dialog = DataImportDialog(self)
        if dialog.exec_() == QDialog.Accepted:
            self.update_status(f"导入 {dialog.import_type} 数据")
            
            # 记录导入前的元素数量
            holes_before = len(self.design_area.get_blast_holes())
            
            # 根据导入类型处理数据
            if dialog.import_type == "hole":
                self.import_hole_data(dialog.imported_data)
            elif dialog.import_type == "work_surface":
                self.import_work_surface_data(dialog.imported_data)
            elif dialog.import_type == "boundary":
                self.import_boundary_data(dialog.imported_data)
            elif dialog.import_type == "text":
                self.import_text_data(dialog.imported_data)
            
            
            # 更新表格数据
            self.design_area.update_table_data()
            
            # 检查是否有新元素被导入
            holes_after = len(self.design_area.get_blast_holes())
            has_new_elements = holes_after > holes_before or dialog.import_type != "hole"
            
            if has_new_elements:
                # 自动适应视图 - 相当于点击"全图"按钮
                self.fit_view()
                self.update_status("数据导入完成，视图已自动调整")
                
                # 询问是否需要手动调整视图
                reply = QMessageBox.question(self, "视图调整", 
                                           "数据导入完成！\n\n"
                                           "系统已自动调整视图显示所有元素。\n"
                                           "如果显示效果不理想，您可以：\n"
                                           "1. 使用工具栏的缩放功能\n"
                                           "2. 再次点击'全图'按钮\n\n"
                                           "是否需要查看导入数据的详细信息？",
                                           QMessageBox.Yes | QMessageBox.No)
                
                if reply == QMessageBox.Yes:
                    self.show_import_summary(dialog.import_type, dialog.imported_data)
            else:
                QMessageBox.information(self, "导入完成", 
                                      f"数据导入处理完成，请检查导入结果")
    
    def _load_default_drilling_equipment(self):
        """加载默认打孔设备数据，作为后备"""
        import json
        import os
        try:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
            equipment_file = os.path.join(data_dir, 'drilling_equipment_parameters.json')

            if os.path.exists(equipment_file):
                with open(equipment_file, 'r', encoding='utf-8') as f:
                    self.drilling_equipment = json.load(f)
            else:
                # 如果JSON文件不存在，使用硬编码的默认值
                self.drilling_equipment = [
                    {"设备名称": "YGZ-90凿岩机", "价格(元/m)": "45.0", "安全距离(m)": "5.0", "孔径": "110", "适用场景": "中硬岩层、隧道掘进"},
                    {"设备名称": "YT-28气腿式凿岩机", "价格(元/m)": "35.0", "安全距离(m)": "3.0", "孔径": "90", "适用场景": "小型矿山、巷道钻孔"},
                    {"设备名称": "KY-150潜孔钻机", "价格(元/m)": "80.0", "安全距离(m)": "8.0", "孔径": "150", "适用场景": "露天矿山、深孔爆破"},
                    {"设备名称": "CM-351履带钻机", "价格(元/m)": "120.0", "安全距离(m)": "10.0", "孔径": "200", "适用场景": "大型露天爆破、高台阶作业"},
                    {"设备名称": "手持式电钻", "价格(元/m)": "25.0", "安全距离(m)": "2.0", "孔径": "75", "适用场景": "浅孔爆破、二次破碎"},
                    {"设备名称": "多功能钻机", "价格(元/m)": "95.0", "安全距离(m)": "7.0", "孔径": "165", "适用场景": "复杂地形、多孔径需求"},
                    {"设备名称": "液压凿岩机", "价格(元/m)": "65.0", "安全距离(m)": "6.0", "孔径": "125", "适用场景": "高效自动化作业"},
                    {"设备名称": "气动钻机", "价格(元/m)": "55.0", "安全距离(m)": "4.0", "孔径": "90", "适用场景": "轻型工程、移动灵活"}
                ]
        except Exception as e:
            print(f"加载默认打孔设备时出错: {e}")
            # 出错时也保证有一个空的列表
            self.drilling_equipment = []
    
    def _load_explosive_data(self):
        """加载炸药参数数据"""
        try:
            # 尝试从文件加载
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            file_path = os.path.join(base_dir, 'data', 'explosive_parameters.json')
            
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    import json
                    self.explosive_data = json.load(f)
                    print(f"成功加载炸药参数: {len(self.explosive_data)} 种炸药")
                    
                    # 更新 explosive_types 列表
                    self.explosive_types = [item.get('炸药名称', '未知') for item in self.explosive_data]
            else:
                print(f"炸药参数文件不存在: {file_path}")
                # 使用默认数据
                self.explosive_data = [
                    {"炸药名称": "2号岩石铵梯炸药", "适用场景": "适用于中硬岩石"},
                    {"炸药名称": "3号岩石铵梯炸药", "适用场景": "适用于硬岩石"},
                    {"炸药名称": "4号岩石铵梯炸药", "适用场景": "适用于极硬岩石"},
                    {"炸药名称": "5号岩石铵梯炸药", "适用场景": "适用于特硬岩石"},
                    {"炸药名称": "胶质炸药", "适用场景": "适用于湿润环境"},
                    {"炸药名称": "乳化炸药", "适用场景": "适用于各种岩石，抗水性好"},
                    {"炸药名称": "PETN炸药", "适用场景": "适用于精密爆破"}
                ]
        except Exception as e:
            print(f"加载炸药参数失败: {e}")
            # 使用默认数据
            self.explosive_data = [
                {"炸药名称": "2号岩石铵梯炸药", "适用场景": "适用于中硬岩石"},
                {"炸药名称": "3号岩石铵梯炸药", "适用场景": "适用于硬岩石"},
                {"炸药名称": "4号岩石铵梯炸药", "适用场景": "适用于极硬岩石"},
                {"炸药名称": "5号岩石铵梯炸药", "适用场景": "适用于特硬岩石"},
                {"炸药名称": "胶质炸药", "适用场景": "适用于湿润环境"},
                {"炸药名称": "乳化炸药", "适用场景": "适用于各种岩石，抗水性好"},
                {"炸药名称": "PETN炸药", "适用场景": "适用于精密爆破"}
            ]
    
    def show_import_summary(self, import_type, imported_data):
        """显示导入数据摘要"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
        
        dialog = QDialog(self)
        dialog.setWindowTitle(f"{self.get_import_type_name(import_type)}导入摘要")
        dialog.resize(500, 400)
        
        layout = QVBoxLayout(dialog)
        
        # 创建文本显示区域
        text_edit = QTextEdit()
        text_edit.setReadOnly(True)
        
        # 生成摘要内容
        summary = f"{self.get_import_type_name(import_type)}导入摘要\n"
        summary += "=" * 50 + "\n\n"
        summary += f"导入数量：{len(imported_data)} 条\n\n"
        
        if import_type == "hole":
            holes = self.design_area.get_blast_holes()
            summary += f"当前总炮孔数：{len(holes)} 个\n\n"
            summary += "导入的炮孔信息：\n"
            for i, row in enumerate(imported_data[:10]):  # 只显示前10个
                hole_id = row.get('hole_id', f'H{i+1:03d}')
                x = row.get('x_coord', 0)
                y = row.get('y_coord', 0)
                z = row.get('z_coord', 'N/A')
                depth = row.get('depth', 15)
                summary += f"  {hole_id}: X={x}, Y={y}, Z={z}, 深度={depth}m\n"
            
            if len(imported_data) > 10:
                summary += f"  ...还有 {len(imported_data)-10} 个炮孔\n"
        
        elif import_type == "boundary":
            summary += "导入的边界点坐标：\n"
            for i, row in enumerate(imported_data):
                x = row.get('x_coord', 0)
                y = row.get('y_coord', 0)
                summary += f"  点{i+1}: X={x}, Y={y}\n"
        
        text_edit.setText(summary)
        layout.addWidget(text_edit)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)
        
        dialog.exec_()
    
    def get_import_type_name(self, import_type):
        """获取导入类型的中文名称"""
        type_names = {
            "hole": "炮孔数据",
            "work_surface": "作业面数据", 
            "boundary": "边界线数据",
            "text": "文本标注数据"
        }
        return type_names.get(import_type, import_type)
    
    def print_graphics(self):
        """图形打印"""
        dialog = PrintSettingsDialog(self.design_area, self)
        if dialog.exec_() == QDialog.Accepted:
            self.update_status("图形打印完成")
    
    # 数据导入处理方法
    def import_hole_data(self, data):
        """导入炮孔数据"""
        imported_count = 0
        skipped_count = 0
        skipped_reasons = []
        
        for row in data:
            try:
                x = float(row.get('x_coord', 0))
                y = float(row.get('y_coord', 0))
                z = float(row.get('z_coord', 0)) if row.get('z_coord') is not None else None
                depth = float(row.get('depth', 15))
                hole_id = row.get('hole_id', '')
                
                # 验证爆破水平限制
                if z is not None and hasattr(self, 'blast_level') and hasattr(self, 'step_height'):
                    min_z = self.blast_level - self.step_height
                    max_z = self.blast_level + self.step_height
                    if not (min_z <= z <= max_z):
                        skipped_count += 1
                        skipped_reasons.append(f"孔{hole_id}: Z坐标{z}超出爆破水平范围[{min_z}, {max_z}]")
                        continue
                
                # 创建炮孔
                hole = self.design_area.add_blast_hole(x, y, 12)
                hole.hole_id = hole_id or f"H{imported_count+1:03d}"
                hole.depth = depth
                
                # 设置Z坐标（如果有）
                if z is not None:
                    hole.z_coord = z

                # 强制刷新显示
                hole.update_text_label()
                hole.update()
                
                imported_count += 1
                
            except (ValueError, TypeError) as e:
                skipped_count += 1
                skipped_reasons.append(f"孔{hole_id}: 数据格式错误 - {str(e)}")
                continue
        
        # 显示导入结果
        if skipped_count > 0:
            message = f"炮孔导入完成：\n"
            message += f"成功导入：{imported_count} 个炮孔\n"
            message += f"跳过：{skipped_count} 个炮孔\n\n"
            if skipped_reasons:
                message += "跳过原因：\n" + "\n".join(skipped_reasons[:3])
                if len(skipped_reasons) > 3:
                    message += f"\n...还有 {len(skipped_reasons)-3} 个"
            QMessageBox.information(self, "导入结果", message)
    
    def import_work_surface_data(self, data):
        """导入作业面数据"""
        from PyQt5.QtCore import QPointF
        points = []
        for row in data:
            try:
                x = float(row.get('x_coord', 0))
                y = float(row.get('y_coord', 0))
                points.append(QPointF(x, y))
            except (ValueError, TypeError):
                continue
        
        if points:
            self.design_area.add_work_surface(points)
    
    def import_boundary_data(self, data):
        """导入边界线数据"""
        points = []
        for row in data:
            try:
                x = float(row.get('x_coord', 0))
                y = float(row.get('y_coord', 0))
                points.append((x, y))
            except (ValueError, TypeError):
                continue
        
        # 连接相邻的点为边界线
        for i in range(len(points) - 1):
            x1, y1 = points[i]
            x2, y2 = points[i + 1]
            self.design_area.add_boundary_line(x1, y1, x2, y2)
    
    def import_text_data(self, data):
        """导入文本数据"""
        for row in data:
            try:
                x = float(row.get('x_coord', 0))
                y = float(row.get('y_coord', 0))
                text = row.get('text', row.get('hole_id', '文本'))
                self.design_area.add_text(x, y, text)
            except (ValueError, TypeError):
                continue
    
    # 参数设置方法
    def open_rock_parameters(self):
        """打开岩石参数对话框"""
        dialog = RockParameterDialog(self)
        dialog.exec_()
    
    def open_explosive_parameters(self):
        """打开炸药参数对话框"""
        dialog = ExplosiveParameterDialog(self)
        dialog.exec_()
    
    def open_drilling_equipment(self):
        """打开打孔设备对话框"""
        dialog = DrillingEquipmentDialog(self)
        dialog.exec_()
    
    # 炮孔初步设计功能方法
    def start_add_hole(self):
        """开始添加炮孔"""
        self.design_area.start_add_hole_mode()
        self.update_status("进入添加炮孔模式，点击设计图添加炮孔")
    
    def add_hole_by_coordinate(self):
        """通过坐标添加炮孔"""
        self.design_area.add_hole_by_coordinate()
    
    def start_delete_hole(self):
        """开始删除炮孔"""
        self.design_area.start_delete_hole_mode() 
        self.update_status("进入删除炮孔模式，点击要删除的炮孔")
    
    def clear_all_holes(self):
        """清空所有炮孔"""
        self.design_area.clear_all_holes()
    
    def start_move_hole(self):
        """开始移动炮孔"""
        self.design_area.start_move_hole_mode()
        self.update_status("进入移动炮孔模式，拖动炮孔到新位置")
    
    def modify_hole_coordinates(self):
        """修改炮孔坐标"""
        self.design_area.modify_hole_coordinates()
    
    def modify_hole_data(self):
        """修改炮孔数据"""
        self.design_area.modify_hole_data()
    
    def modify_hole_avv(self):
        """修改炮孔AVV参数"""
        self.design_area.modify_hole_avv()
    
    def exit_hole_operation(self):
        """退出炮孔操作模式"""
        self.design_area.exit_hole_operation_mode()
        self.update_status("已退出炮孔操作模式")
    
    # 设计功能方法
    def design_holes(self):
        """设计炮孔"""
        self.update_status("进入炮孔设计模式")
        self.design_area.set_mode("hole_design")
    
    def set_hole_types(self):
        """设置孔类型"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox
        
        # 检查是否有选中的炮孔
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'hole_type')]
        
        if not hole_items:
            QMessageBox.warning(self, "提示", "请先选择要设置类型的炮孔！")
            return
        
        hole_types = ["主炮孔", "辅助孔", "缓冲孔", "周边孔"]
        hole_type, ok = QInputDialog.getItem(self, "选择孔类型", "孔类型:", hole_types, 0, False)
        if ok:
            self.current_hole_type = hole_type
            
            # 更新所有选中炮孔的类型和颜色
            updated_count = 0
            for hole_item in hole_items:
                hole_item.hole_type = hole_type
                hole_item.update_hole_color()  # 更新颜色
                updated_count += 1
            
            # 更新设计区域
            self.design_area.update_table_data()
            self.design_area.save_state()

            
            self.update_status(f"已将 {updated_count} 个炮孔设置为: {hole_type}")
    
    def set_hole_diameter(self):
        """设置孔径"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox
        diameter, ok = QInputDialog.getDouble(self, "设置孔径", "孔径(mm):", 110, 50, 300, 1)
        if ok:
            self.current_hole_diameter = diameter
            self.update_status(f"当前孔径: {diameter}mm")
            
            # 询问是否更新现有炮孔
            holes = self.design_area.get_blast_holes()
            if holes:
                reply = QMessageBox.question(
                    self, "更新现有炮孔", 
                    f"是否将所有现有炮孔的孔径更新为 {diameter}mm？\n这将同时改变炮孔的显示大小。",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    updated_count = 0
                    for hole in holes:
                        hole.diameter_value = diameter  # 更新孔径值
                        hole.update_diameter(diameter)  # 更新视觉大小
                        hole.update_text_label()  # 更新标签显示
                        updated_count += 1
                    
                    # 更新设计区域
                    self.design_area.update_table_data()
                    self.design_area.save_state()
                    
                    self.update_status(f"已更新 {updated_count} 个炮孔的孔径为 {diameter}mm")
    
    def load_global_parameters(self):
        """加载全局参数"""
        try:
            import json
            import os
            
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
            cache_file = os.path.join(data_dir, 'global_parameters.json')
            
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    params = json.load(f)
                
                # 更新岩石类型
                if 'rock_types' in params:
                    self.rock_types = params['rock_types']
                
                # 更新炸药类型
                if 'explosive_types' in params:
                    self.explosive_types = params['explosive_types']
                
                # 更新打孔设备
                if 'drilling_equipment' in params:
                    self.drilling_equipment = params['drilling_equipment']
                
                print(f"已加载全局参数: 岩石类型{len(self.rock_types)}种, 炸药类型{len(self.explosive_types)}种")
                
        except Exception as e:
            print(f"加载全局参数时出错: {e}")
    
    def update_rock_types(self, rock_types):
        """更新岩石类型列表"""
        self.rock_types = rock_types
        self.update_status(f"岩石类型已更新，共{len(rock_types)}种类型")
        
        # 如果当前选择的岩石类型不在新列表中，重置为第一个
        if self.current_rock_type not in rock_types and rock_types:
            self.current_rock_type = rock_types[0]
            self.update_status(f"当前岩石类型已重置为: {self.current_rock_type}")
    
    def update_explosive_types(self, explosive_types):
        """更新炸药类型列表"""
        self.explosive_types = explosive_types
        self.update_status(f"炸药类型已更新，共{len(explosive_types)}种类型")
        
        # 如果当前选择的炸药类型不在新列表中，重置为第一个
        if self.current_explosive_type not in explosive_types and explosive_types:
            self.current_explosive_type = explosive_types[0]
            self.update_status(f"当前炸药类型已重置为: {self.current_explosive_type}")
    
    def update_drilling_equipment(self, equipment_list):
        """更新打孔设备列表"""
        self.drilling_equipment = equipment_list
        self.update_status(f"打孔设备已更新，共{len(equipment_list)}种设备")
    
    def get_rock_parameters(self, rock_name):
        """获取岩石参数"""
        try:
            import json
            import os
            
            data_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data')
            rock_file = os.path.join(data_dir, 'rock_parameters.json')
            
            if os.path.exists(rock_file):
                with open(rock_file, 'r', encoding='utf-8') as f:
                    rock_data = json.load(f)
                
                for rock in rock_data:
                    if rock.get('岩石名称') == rock_name:
                        return rock
            
        except Exception as e:
            print(f"获取岩石参数时出错: {e}")
        
        return None
    
    def calculate_explosive_amount(self, hole_volume, rock_type):
        """根据岩石类型计算炸药量"""
        rock_params = self.get_rock_parameters(rock_type)
        if not rock_params:
            return 25.0  # 默认值
        
        try:
            # 获取爆破阻力参数
            resistance_str = rock_params.get('爆破阻力', '5.0-8.0')
            if '-' in resistance_str:
                resistance_values = resistance_str.split('-')
                avg_resistance = (float(resistance_values[0]) + float(resistance_values[1])) / 2
            else:
                avg_resistance = float(resistance_str)
            
            # 简单的炸药量计算公式
            explosive_amount = hole_volume * avg_resistance * 0.8
            return round(explosive_amount, 1)
            
        except Exception as e:
            print(f"计算炸药量时出错: {e}")
            return 25.0
    
    def set_rock_type(self):
        """设置岩石类型"""
        from PyQt5.QtWidgets import QInputDialog
        rock_type, ok = QInputDialog.getItem(self, "选择岩石类型", "岩石类型:", self.rock_types, 0, False)
        if ok:
            self.current_rock_type = rock_type
            self.update_status(f"当前岩石类型: {rock_type}")
            
            # 更新所有炮孔的岩石类型并重新计算炸药量
            holes = self.design_area.get_blast_holes()
            if holes:
                updated_count = 0
                for hole in holes:
                    # 设置岩石类型
                    hole.rock_type = rock_type
                    
                    # 重新计算炸药量
                    hole_volume = getattr(hole, 'volume', 15.0 * 0.11 * 0.11 * 3.14159 / 4)  # 默认15m深，110mm直径
                    new_amount = self.calculate_explosive_amount(hole_volume, rock_type)
                    hole.explosive_amount = new_amount
                    
                    # 更新显示
                    hole.update_text_label()
                    hole.update_rock_display()
                    updated_count += 1
                
                if updated_count > 0:
                    # 更新后保存状态
                    self.design_area.save_state()
                    self.update_status(f"已将{updated_count}个炮孔的岩石类型设置为: {rock_type}，并重新计算了炸药量")
    
    def start_rock_type_setting(self):
        """开始岩石类型设置模式"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox
        
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'hole_id')]
        
        if not hole_items:
            QMessageBox.warning(self, "提示", "请先选择要设置岩石类型的炮孔！")
            return
        
        # 进入岩石设置的特殊显示模式
        self.design_area.start_rock_setting_mode()
        
        rock_type, ok = QInputDialog.getItem(self, "选择岩石类型", 
                                           f"为选中的 {len(hole_items)} 个炮孔设置岩石类型:", 
                                           self.rock_types, 0, False)
        if ok:
            self.current_rock_type = rock_type
            
            updated_count = 0
            for hole in hole_items:
                hole.rock_type = rock_type
                hole.update_text_label()
                updated_count += 1
            
            self.design_area.save_state()
            self.update_status(f"已将 {updated_count} 个炮孔的岩石类型设置为: {rock_type}")

        # 退出特殊显示模式
        self.design_area.exit_rock_setting_mode()
    
    def set_all_holes_rock_type(self):
        """为所有孔设置岩石类型"""
        from PyQt5.QtWidgets import QInputDialog
        
        holes = self.design_area.get_blast_holes()
        if not holes:
            QMessageBox.warning(self, "设置失败", "没有可设置的炮孔")
            return
        
        rock_type, ok = QInputDialog.getItem(self, "选择岩石类型", 
                                           f"为所有{len(holes)}个炮孔设置岩石类型:", 
                                           self.rock_types, 0, False)
        if ok:
            updated_count = 0
            for hole in holes:
                hole.rock_type = rock_type
                
                # 重新计算炸药量
                hole_volume = math.pi * (hole.diameter_value/2000)**2 * hole.depth
                hole.explosive_amount = self.calculate_explosive_amount(hole_volume, rock_type)
                
                # 更新显示
                hole.update_text_label()
                hole.update_rock_display()  # 新增方法，显示岩石信息
                updated_count += 1
            
            # 保存状态
            self.design_area.save_state()
    
            self.update_status(f"已将{updated_count}个炮孔的岩石类型设置为: {rock_type}，并重新计算了炸药量")
    
    def set_explosive_type(self):
        """设置炸药类型 - 显示子菜单选择模式"""
        from PyQt5.QtWidgets import QMenu, QAction
        
        # 创建子菜单
        menu = QMenu("炸药设置模式", self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #316AC5;
                color: white;
                border-radius: 3px;
            }
        """)
        
        # 添加子菜单项
        interactive_action = QAction("炸药", self)
        interactive_action.setStatusTip("交互式设置单个炮孔的炸药类型和装药结构")
        interactive_action.triggered.connect(self.start_explosive_setting)
        
        all_holes_action = QAction("所有孔", self)
        all_holes_action.setStatusTip("批量设置所有炮孔的炸药类型和装药结构")
        all_holes_action.triggered.connect(self.set_all_holes_explosive)
        
        menu.addAction(interactive_action)
        menu.addAction(all_holes_action)
        
        # 在鼠标位置显示菜单
        menu.exec_(self.mapToGlobal(self.rect().center()))
    
    def start_explosive_setting(self):
        """开始交互式炸药设置模式 - 参照set_drilling_equipment的实现"""
        from src.dialogs.explosive_design_dialog import ExplosiveDesignDialog
        from PyQt5.QtWidgets import QMessageBox
        
        # 检查是否有选中的炮孔
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'drilling_equipment')]
        
        if not hole_items:
            QMessageBox.warning(self, "提示", "请先选择要设置炸药类型的炮孔！")
            return
        
        # 显示炸药设置对话框，并传入完整的炸药数据列表
        dialog = ExplosiveDesignDialog(self.explosive_data, self)
        dialog.setWindowTitle(f"为选中的 {len(hole_items)} 个炮孔设置炸药")
        
        self.design_area.start_explosive_setting_mode()
        
        if dialog.exec_() == dialog.Accepted:
            selection = dialog.get_selection()
            explosive_type = selection['explosive_type']
            charge_structure = selection['charge_structure']
            
            # 更新当前配置
            self.current_explosive_type = explosive_type
            self.current_charge_structure = charge_structure
            
            # 直接更新选中的炮孔
            updated_count = 0
            for hole in hole_items:
                hole.explosive_type = explosive_type
                hole.charge_structure = charge_structure
                
                # 更新所有相关显示 - 参照set_drilling_equipment的模式
                hole.update_text_label()
                hole.update_rock_display()  # 确保岩石标签也得到更新
                if hasattr(hole, 'update_explosive_display'):
                    hole.update_explosive_display()
                
                updated_count += 1
            
            self.design_area.update_table_data()
            self.design_area.save_state()
            self.update_status(f"已将选中的 {updated_count} 个炮孔的炸药设置为: {explosive_type} ({charge_structure})")
        
        self.design_area.exit_explosive_setting_mode()
    
    def set_all_holes_explosive(self):
        """批量设置所有炮孔的炸药类型和装药结构"""
        from src.dialogs.explosive_design_dialog import ExplosiveDesignDialog
        from PyQt5.QtWidgets import QMessageBox
        
        holes = self.design_area.get_blast_holes()
        if not holes:
            QMessageBox.information(self, "提示", "当前没有炮孔！")
            return
        
        # 显示炸药设置对话框，并传入完整的炸药数据列表
        dialog = ExplosiveDesignDialog(self.explosive_data, self)
        dialog.setWindowTitle("批量设置所有炮孔炸药")
        
        self.design_area.start_explosive_setting_mode()
        
        if dialog.exec_() == dialog.Accepted:
            selection = dialog.get_selection()
            explosive_type = selection['explosive_type']
            charge_structure = selection['charge_structure']
            
            # 更新当前配置
            self.current_explosive_type = explosive_type
            self.current_charge_structure = charge_structure
            
            # 更新所有炮孔的炸药信息
            updated_count = 0
            for hole in holes:
                hole.explosive_type = explosive_type
                hole.charge_structure = charge_structure
                # 更新显示
                hole.update_text_label()
                hole.update_explosive_display()
                updated_count += 1
            
            self.design_area.update_table_data()
            self.design_area.save_state()

            self.update_status(f"已将{updated_count}个炮孔的炸药设置为: {explosive_type} ({charge_structure})")
        
        self.design_area.exit_explosive_setting_mode()
    
    def set_spacing(self):
        """设置间排距 - 显示子菜单选择模式"""
        from PyQt5.QtWidgets import QMenu, QAction
        
        # 创建子菜单
        menu = QMenu("间排距设置模式", self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #316AC5;
                color: white;
                border-radius: 3px;
            }
        """)
        
        # 添加子菜单项
        selected_action = QAction("间距排距", self)
        selected_action.setStatusTip("为选中的炮孔设置间距和排距")
        selected_action.triggered.connect(self.set_spacing_for_selected_holes)
        
        all_holes_action = QAction("所有孔", self)
        all_holes_action.setStatusTip("为所有炮孔设置间距和排距")
        all_holes_action.triggered.connect(self.set_all_holes_spacing)
        
        menu.addAction(selected_action)
        menu.addAction(all_holes_action)
        
        # 在鼠标位置显示菜单
        menu.exec_(self.mapToGlobal(self.rect().center()))
    
    def set_spacing_for_selected_holes(self):
        """为选中的炮孔设置间排距（参照set_drilling_equipment的实现）"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QDoubleSpinBox, QPushButton, QMessageBox
        
        # 检查是否有选中的炮孔
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'drilling_equipment')]
        
        if not hole_items:
            QMessageBox.warning(self, "提示", "请先选择要设置间排距的炮孔！")
            return
        
        print(f"找到 {len(hole_items)} 个选中的炮孔")
        for i, hole in enumerate(hole_items):
            print(f"  选中炮孔 {i+1}: {hole.hole_id}")
        
        dialog = QDialog(self)
        dialog.setWindowTitle(f"为选中的 {len(hole_items)} 个炮孔设置间排距")
        dialog.resize(350, 200)
        
        layout = QVBoxLayout()
        
        # 提示信息
        info_label = QLabel(f"将为 {len(hole_items)} 个选中的炮孔设置间排距")
        info_label.setStyleSheet("color: blue; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)
        
        # 炮孔间距
        spacing_layout = QHBoxLayout()
        spacing_layout.addWidget(QLabel("炮孔间距(m):"))
        spacing_spin = QDoubleSpinBox()
        spacing_spin.setRange(1.0, 10.0)
        spacing_spin.setValue(3.5)
        spacing_spin.setDecimals(1)
        spacing_layout.addWidget(spacing_spin)
        layout.addLayout(spacing_layout)
        
        # 排距
        row_spacing_layout = QHBoxLayout()
        row_spacing_layout.addWidget(QLabel("排距(m):"))
        row_spacing_spin = QDoubleSpinBox()
        row_spacing_spin.setRange(1.0, 10.0)
        row_spacing_spin.setValue(4.0)
        row_spacing_spin.setDecimals(1)
        row_spacing_layout.addWidget(row_spacing_spin)
        layout.addLayout(row_spacing_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 连接信号
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)
        
        # 进入间排距设置的特殊显示模式
        self.design_area.start_spacing_setting_mode()
        
        if dialog.exec_() == QDialog.Accepted:
            spacing = spacing_spin.value()
            row_spacing = row_spacing_spin.value()
            
            print(f"用户设置的间距: {spacing}m")
            print(f"用户设置的排距: {row_spacing}m")
            
            # 更新当前配置
            self.current_spacing = spacing
            self.current_row_spacing = row_spacing
            
            # 直接更新选中的炮孔
            updated_count = 0
            print(f"开始处理 {len(hole_items)} 个炮孔")
            for i, hole in enumerate(hole_items):
                print(f"  处理第 {i+1} 个炮孔: {hole.hole_id}")
                print(f"    原间距: {getattr(hole, 'spacing', 3.5)}m")
                print(f"    原排距: {getattr(hole, 'row_spacing', 4.0)}m")
                
                hole.spacing = spacing
                hole.row_spacing = row_spacing
                print(f"    新间距: {hole.spacing}m")
                print(f"    新排距: {hole.row_spacing}m")
                
                # 更新所有相关显示 - 参照set_drilling_equipment的模式
                print(f"    调用 hole.update_text_label()")
                hole.update_text_label()
                print(f"    调用 hole.update_rock_display()")
                hole.update_rock_display()  # 确保岩石标签也得到更新
                if hasattr(hole, 'update_explosive_display'):
                    print(f"    调用 hole.update_explosive_display()")
                    hole.update_explosive_display()
                
                print(f"    设置炮孔 {hole.hole_id} 间排距为: {spacing}m × {row_spacing}m - 完成")
                updated_count += 1
            
            print(f"保存状态并更新表格...")
            self.design_area.update_table_data()
            self.design_area.save_state()

            
            success_msg = f"已将选中的 {updated_count} 个炮孔的间排距设置为: 间距{spacing}m, 排距{row_spacing}m"
            print(f"SUCCESS: {success_msg}")
            self.update_status(success_msg)
            print("=== 间排距设置完成 ===\n")
        
        # 退出特殊显示模式
        self.design_area.exit_spacing_setting_mode()
    
    def set_all_holes_spacing(self):
        """为所有炮孔设置间排距"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QDoubleSpinBox, QPushButton, QMessageBox
        
        holes = self.design_area.get_blast_holes()
        if not holes:
            QMessageBox.information(self, "提示", "当前没有炮孔！")
            return
        
        dialog = QDialog(self)
        dialog.setWindowTitle(f"为所有 {len(holes)} 个炮孔设置间排距")
        dialog.resize(350, 200)
        
        layout = QVBoxLayout()
        
        # 提示信息
        info_label = QLabel(f"将为所有 {len(holes)} 个炮孔设置间排距")
        info_label.setStyleSheet("color: blue; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)
        
        # 炮孔间距
        spacing_layout = QHBoxLayout()
        spacing_layout.addWidget(QLabel("炮孔间距(m):"))
        spacing_spin = QDoubleSpinBox()
        spacing_spin.setRange(1.0, 10.0)
        spacing_spin.setValue(3.5)
        spacing_spin.setDecimals(1)
        spacing_layout.addWidget(spacing_spin)
        layout.addLayout(spacing_layout)
        
        # 排距
        row_spacing_layout = QHBoxLayout()
        row_spacing_layout.addWidget(QLabel("排距(m):"))
        row_spacing_spin = QDoubleSpinBox()
        row_spacing_spin.setRange(1.0, 10.0)
        row_spacing_spin.setValue(4.0)
        row_spacing_spin.setDecimals(1)
        row_spacing_layout.addWidget(row_spacing_spin)
        layout.addLayout(row_spacing_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 连接信号
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)
        
        # 进入间排距设置的特殊显示模式
        self.design_area.start_spacing_setting_mode()
        
        if dialog.exec_() == QDialog.Accepted:
            spacing = spacing_spin.value()
            row_spacing = row_spacing_spin.value()
            
            # 更新当前配置
            self.current_spacing = spacing
            self.current_row_spacing = row_spacing
            
            # 更新所有炮孔
            updated_count = 0
            for hole in holes:
                hole.spacing = spacing
                hole.row_spacing = row_spacing
                
                # 更新显示
                hole.update_text_label()
                hole.update_rock_display()
                if hasattr(hole, 'update_explosive_display'):
                    hole.update_explosive_display()
                
                updated_count += 1
            
            self.design_area.update_table_data()
            self.design_area.save_state()

            self.update_status(f"已将所有 {updated_count} 个炮孔的间排距设置为: 间距{spacing}m, 排距{row_spacing}m")
        
        # 退出特殊显示模式
        self.design_area.exit_spacing_setting_mode()
    
    def connect_rows(self):
        """连接成排 - 显示子菜单选择模式"""
        from PyQt5.QtWidgets import QMenu, QAction
        
        # 创建子菜单
        menu = QMenu("连接成排模式", self)
        menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #d0d0d0;
                border-radius: 5px;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 20px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #316AC5;
                color: white;
                border-radius: 3px;
            }
        """)
        
        # 添加子菜单项
        selected_action = QAction("连接", self)
        selected_action.setStatusTip("连接选中的炮孔成排")
        selected_action.triggered.connect(self.connect_selected_holes)
        
        auto_action = QAction("自动连接", self)
        auto_action.setStatusTip("自动连接所有炮孔成排")
        auto_action.triggered.connect(self.auto_connect_all_holes)
        
        menu.addAction(selected_action)
        menu.addAction(auto_action)
        
        # 在鼠标位置显示菜单
        menu.exec_(self.mapToGlobal(self.rect().center()))
    
    def connect_selected_holes(self):
        """连接选中的炮孔成排（参照set_drilling_equipment的实现）"""
        from PyQt5.QtWidgets import QMessageBox
        
        # 检查是否有选中的炮孔
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'drilling_equipment')]
        
        if len(hole_items) < 2:
            QMessageBox.warning(self, "提示", "请先选择至少2个炮孔进行连接！")
            return
        
        print(f"找到 {len(hole_items)} 个选中的炮孔")
        for i, hole in enumerate(hole_items):
            print(f"  选中炮孔 {i+1}: {hole.hole_id}")
        
        # 按位置排序选中的炮孔（先按Y坐标，再按X坐标）
        hole_items.sort(key=lambda h: (round(h.pos().y()/10)*10, h.pos().x()))
        
        # 连接相邻的炮孔
        connected_count = 0
        print(f"开始连接 {len(hole_items)} 个炮孔")
        for i in range(len(hole_items) - 1):
            hole1 = hole_items[i]
            hole2 = hole_items[i + 1]
            
            x1, y1 = hole1.pos().x(), hole1.pos().y()
            x2, y2 = hole2.pos().x(), hole2.pos().y()
            
            print(f"  连接炮孔 {hole1.hole_id} 到 {hole2.hole_id}")
            print(f"    从 ({x1:.1f}, {y1:.1f}) 到 ({x2:.1f}, {y2:.1f})")
            
            # 添加连接线
            connection_line = self.design_area.add_connection_line(x1, y1, x2, y2)
            if connection_line:
                # 为连接线添加标识信息
                connection_line.hole1_id = hole1.hole_id
                connection_line.hole2_id = hole2.hole_id
                connection_line.connection_type = "row_connection"
                connected_count += 1
        
        success_msg = f"连接成排完成，共连接 {connected_count} 条线"
        print(f"SUCCESS: {success_msg}")
        self.update_status(success_msg)
        self.design_area.save_state()
        print("=== 连接成排完成 ===\n")
    
    def auto_connect_all_holes(self):
        """自动连接所有炮孔成排"""
        from PyQt5.QtWidgets import QMessageBox
        
        holes = self.design_area.get_blast_holes()
        if len(holes) < 2:
            QMessageBox.warning(self, "连接失败", "至少需要2个炮孔才能连接成排")
            return
        
        print(f"找到 {len(holes)} 个炮孔，开始自动连接成排")
        
        # 简单的按Y坐标分组连接
        rows = {}
        for hole in holes:
            y = round(hole.pos().y()/10)*10  # 按10像素为单位分组
            if y not in rows:
                rows[y] = []
            rows[y].append(hole)
        
        print(f"分组结果: {len(rows)} 排")
        for y, row_holes in rows.items():
            print(f"  Y={y}: {len(row_holes)} 个炮孔")
        
        connected_count = 0
        for y, row_holes in rows.items():
            if len(row_holes) >= 2:
                # 按X坐标排序
                row_holes.sort(key=lambda h: h.pos().x())
                print(f"处理 Y={y} 排的 {len(row_holes)} 个炮孔")
                
                # 连接相邻的孔
                for i in range(len(row_holes) - 1):
                    hole1 = row_holes[i]
                    hole2 = row_holes[i + 1]
                    
                    x1, y1 = hole1.pos().x(), hole1.pos().y()
                    x2, y2 = hole2.pos().x(), hole2.pos().y()
                    
                    print(f"  连接炮孔 {hole1.hole_id} 到 {hole2.hole_id}")
                    
                    # 添加连接线
                    connection_line = self.design_area.add_connection_line(x1, y1, x2, y2)
                    if connection_line:
                        # 为连接线添加标识信息
                        connection_line.hole1_id = hole1.hole_id
                        connection_line.hole2_id = hole2.hole_id
                        connection_line.connection_type = "auto_row_connection"
                        connected_count += 1
        
        self.design_area.save_state()

        
        success_msg = f"自动连接成排完成，共连接 {connected_count} 条线"
        print(f"SUCCESS: {success_msg}")
        self.update_status(success_msg)
    
    def edit_explosive_amount(self):
        """编辑炸药量 - 精细化设计功能"""
        from PyQt5.QtWidgets import QMessageBox
        
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'hole_id')]
        
        if not hole_items:
            QMessageBox.warning(self, "提示", "请先选择要编辑炸药量的炮孔！")
            return
        
        if len(hole_items) > 1:
            QMessageBox.warning(self, "提示", "请选择单个炮孔进行炸药量编辑！")
            return
        
        self.design_area.start_explosive_amount_setting_mode()
        self.open_explosive_amount_dialog(hole_items[0])
        self.design_area.exit_explosive_amount_setting_mode()
    
    def open_explosive_amount_dialog(self, hole_item):
        """打开炸药量编辑对话框"""
        try:
            from .dialogs.explosive_amount_dialog import ExplosiveAmountDialog
            
            dialog = ExplosiveAmountDialog(hole_item, self, self)
            dialog.data_updated.connect(lambda data: self.update_hole_explosive_data(hole_item, data))
            
            if dialog.exec_() == QDialog.Accepted:
                self.update_status(f"炮孔 {hole_item.hole_id} 炸药量编辑完成")
            else:
                self.update_status("取消编辑炸药量")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开炸药量编辑对话框失败:\n{str(e)}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")
    
    def update_hole_explosive_data(self, hole_item, data):
        """更新炮孔的炸药数据"""
        try:
            # 更新炮孔对象的属性
            hole_item.depth = data['measured_depth']
            hole_item.backfill_length = data['backfill_length']
            hole_item.explosive_amount = data['explosive_amount']
            hole_item.project_type = data['project_type']
            hole_item.unit_charge = data['unit_charge']
            
            # 更新装药长度
            hole_item.charge_length = data['measured_depth'] - data['backfill_length']
            
            # 更新填塞长度
            if 'min_stemming' in data:
                hole_item.stemming_length = data['min_stemming']
            
                # 更新显示
            hole_item.update_text_label()
            if hasattr(hole_item, 'update_explosive_display'):
                hole_item.update_explosive_display()
            if hasattr(hole_item, 'update_rock_display'):
                hole_item.update_rock_display()
            
            # 更新表格数据
            self.design_area.update_table_data()
            
            # 保存状态
            self.design_area.save_state()
            
            self.update_status(f"炮孔 {hole_item.hole_id} 炸药数据已更新 - 炸药量: {data['explosive_amount']:.2f}kg")
            
        except Exception as e:
            QMessageBox.critical(self, "更新失败", f"更新炮孔炸药数据失败:\n{str(e)}")
    
    def draw_work_surface(self):
        """绘制作业面"""
        self.design_area.start_work_surface_mode()
        self.update_status("进入作业面绘制模式，左键点击绘制点，右键结束绘制")
    
    def draw_boundary(self):
        """绘制边界线"""
        self.design_area.start_boundary_mode()
        self.update_status("进入边界线绘制模式，左键点击绘制点，右键结束绘制并自动闭合")
    
    def add_text(self):
        """添加文本"""
        self.design_area.start_text_mode()
        self.update_status("进入文本添加模式，点击位置添加文本")
    
    # 输出方法
    def generate_network_diagram(self):
        """生成爆破网络图"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QGraphicsView, QGraphicsScene, QPushButton, QHBoxLayout, QLabel
        from PyQt5.QtCore import Qt, QRectF
        from PyQt5.QtGui import QPen, QBrush, QFont, QColor
        
        dialog = QDialog(self)
        dialog.setWindowTitle("爆破网络图")
        dialog.resize(1000, 700)
        
        layout = QVBoxLayout()
        
        # 添加说明标签
        info_label = QLabel("爆破网络图 - 显示炮孔布局、类型、作业面、边界线和文本标注")
        info_label.setStyleSheet("font-weight: bold; margin: 5px; color: blue;")
        layout.addWidget(info_label)
        
        # 创建网络图视图
        scene = QGraphicsScene()
        view = QGraphicsView(scene)
        
        # 绘制所有设计元素
        self._draw_all_elements_to_scene(scene)
        
        # 添加图例
        self._add_legend_to_scene(scene)
        
        # 适应视图
        scene_rect = scene.itemsBoundingRect()
        if not scene_rect.isEmpty():
            # 为图例预留更大的空间
            scene.setSceneRect(scene_rect.adjusted(-50, -50, 300, 200))  # 右侧和底部留出更多空间
            view.fitInView(scene.sceneRect(), Qt.KeepAspectRatio)
            view.setMinimumSize(800, 600)  # 设置最小尺寸
        
        layout.addWidget(view)
        
        # 添加按钮
        btn_layout = QHBoxLayout()
        export_btn = QPushButton("导出图片")
        export_btn.setStyleSheet("QPushButton { padding: 8px 16px; font-weight: bold; }")
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("QPushButton { padding: 8px 16px; }")
        
        export_btn.clicked.connect(lambda: self._export_network_diagram(scene))
        close_btn.clicked.connect(dialog.close)
        btn_layout.addWidget(export_btn)
        btn_layout.addWidget(close_btn)
        layout.addLayout(btn_layout)
        
        dialog.setLayout(layout)
        dialog.exec_()
        
        self.update_status("爆破网络图已生成")
    
    def _draw_all_elements_to_scene(self, scene):
        """绘制所有设计元素到场景"""
        from PyQt5.QtGui import QPen, QBrush, QFont
        from PyQt5.QtCore import Qt
        
        # 首先绘制网格背景
        self._draw_grid_to_scene(scene)
        
        # 1. 绘制边界线
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type'):
                if item.item_type == 'boundary_line':
                    # 直线边界
                    line = item.line()
                    scene.addLine(line, QPen(Qt.blue, 2, Qt.DashLine))
                elif item.item_type == 'boundary':
                    # 多边形边界
                    if hasattr(item, 'points') and len(item.points) >= 3:
                        pen = QPen(Qt.blue, 2, Qt.DashLine)
                        for i in range(len(item.points)):
                            next_i = (i + 1) % len(item.points)
                            scene.addLine(item.points[i].x(), item.points[i].y(),
                                        item.points[next_i].x(), item.points[next_i].y(), pen)
                        # 绘制顶点
                        for point in item.points:
                            scene.addEllipse(point.x()-2, point.y()-2, 4, 4, 
                                           QPen(Qt.darkBlue), QBrush(Qt.blue))
        
        # 2. 绘制作业面
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type') and item.item_type == 'work_surface':
                if hasattr(item, 'points') and len(item.points) >= 2:
                    pen = QPen(Qt.red, 3, Qt.SolidLine)
                    for i in range(len(item.points) - 1):
                        scene.addLine(item.points[i].x(), item.points[i].y(),
                                    item.points[i+1].x(), item.points[i+1].y(), pen)
        
        # 3. 绘制炮孔
        holes = self.design_area.get_blast_holes()
        for hole in holes:
            x, y = hole.pos().x(), hole.pos().y()
            
            # 根据孔类型选择颜色
            hole_color = self._get_hole_display_color(hole)
            
            # 绘制炮孔圆圈
            diameter = max(12, min(24, getattr(hole, 'diameter_value', 110) * 24 / 110))
            circle = scene.addEllipse(x-diameter/2, y-diameter/2, diameter, diameter, 
                                    QPen(Qt.black, 2), QBrush(hole_color))
            
            # 添加炮孔编号（简化显示）
            hole_id = getattr(hole, 'hole_id', 'H001')
            # 只显示编号的后3位数字
            if len(hole_id) > 3:
                display_id = hole_id[-3:]
            else:
                display_id = hole_id
            
            text = scene.addText(display_id, QFont("Arial", 9, QFont.Bold))
            text.setPos(x-12, y-30)
            text.setDefaultTextColor(Qt.black)
            
            # 添加孔径信息（简化）
            diameter_mm = getattr(hole, 'diameter_value', 110)
            diameter_text = scene.addText(f"φ{diameter_mm:.0f}", QFont("Arial", 7))
            diameter_text.setPos(x-15, y+12)
            diameter_text.setDefaultTextColor(Qt.blue)
        
        # 4. 绘制文本标注（过滤掉炮孔相关的文本）
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type') and item.item_type == 'text_annotation':
                text_content = item.toPlainText()
                pos = item.pos()
                text = scene.addText(text_content, QFont("SimHei", 10))
                text.setPos(pos)
                text.setDefaultTextColor(Qt.black)
            elif hasattr(item, 'toPlainText') and not hasattr(item, 'item_type'):
                # 兼容旧版本文本，但过滤掉可能是炮孔标签的文本
                text_content = item.toPlainText()
                if not self._is_hole_label_text(text_content):
                    pos = item.pos()
                    text = scene.addText(text_content, QFont("SimHei", 10))
                    text.setPos(pos)
                    text.setDefaultTextColor(Qt.black)
        
        # 5. 绘制参考点
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type') and item.item_type == 'reference_point':
                pos = item.pos()
                scene.addEllipse(pos.x(), pos.y(), 6, 6, QPen(Qt.darkRed, 2), QBrush(Qt.red))
            elif hasattr(item, 'rect') and item.rect().width() == 6 and not hasattr(item, 'item_type'):
                # 兼容旧版本参考点
                pos = item.pos()
                scene.addEllipse(pos.x(), pos.y(), 6, 6, QPen(Qt.darkRed, 2), QBrush(Qt.red))
    
    def _draw_grid_to_scene(self, scene):
        """绘制网格背景"""
        from PyQt5.QtGui import QPen
        from PyQt5.QtCore import Qt
        
        # 获取所有元素的边界
        holes = self.design_area.get_blast_holes()
        if not holes:
            return
        
        # 计算边界范围
        min_x = min(hole.pos().x() for hole in holes) - 100
        max_x = max(hole.pos().x() for hole in holes) + 100
        min_y = min(hole.pos().y() for hole in holes) - 100
        max_y = max(hole.pos().y() for hole in holes) + 100
        
        # 网格间距
        grid_spacing = 50
        
        # 绘制垂直网格线
        pen = QPen(Qt.lightGray, 0.5, Qt.DotLine)
        x = int(min_x / grid_spacing) * grid_spacing
        while x <= max_x:
            scene.addLine(x, min_y, x, max_y, pen)
            x += grid_spacing
        
        # 绘制水平网格线
        y = int(min_y / grid_spacing) * grid_spacing
        while y <= max_y:
            scene.addLine(min_x, y, max_x, y, pen)
            y += grid_spacing
    
    def _is_hole_label_text(self, text_content):
        """判断是否是炮孔标签文本（用于过滤）"""
        if not text_content:
            return False
        
        # 检查是否包含炮孔相关的关键词
        hole_keywords = ['H0', 'φ', 'mm', 'kg', '花岗岩', '石灰岩', '砂岩', '页岩', 
                        '玄武岩', '大理岩', '片麻岩', '辉绿岩', '岩石', 'm深']
        
        for keyword in hole_keywords:
            if keyword in text_content:
                return True
        
        # 检查是否是纯数字编号
        if text_content.isdigit() and len(text_content) <= 4:
            return True
        
        return False
    
    def _get_hole_display_color(self, hole):
        """获取炮孔显示颜色"""
        # 优先按特殊孔类型设置颜色
        special_type = getattr(hole, 'special_hole_type', '普通孔')
        if special_type == "起始孔":
            return QColor(255, 0, 0)    # 红色
        elif special_type == "死孔":
            return QColor(192, 192, 192)  # 亮灰色
        elif special_type == "失火孔":
            return QColor(255, 255, 0)  # 黄色
        elif special_type == "普通孔":
            return QColor(0, 0, 255)    # 蓝色
        
        # 按常规孔类型设置颜色
        hole_type = getattr(hole, 'hole_type', '主炮孔')
        if hole_type == "主炮孔":
            return QColor(255, 255, 0)  # 黄色
        elif hole_type == "辅助孔":
            return QColor(0, 255, 255)  # 青色
        elif hole_type == "缓冲孔":
            return QColor(0, 255, 0)    # 绿色
        elif hole_type == "周边孔":
            return QColor(0, 0, 255)    # 蓝色
        elif hole_type == "保护孔":
            return QColor(255, 0, 0)    # 红色
        else:
            return QColor(255, 255, 0)  # 默认黄色
    
    def _add_legend_to_scene(self, scene):
        """在场景中添加图例"""
        from PyQt5.QtGui import QFont, QColor, QPen, QBrush
        from PyQt5.QtCore import Qt
        
        # 计算图例位置（场景右侧）
        scene_rect = scene.itemsBoundingRect()
        legend_x = scene_rect.right() + 30
        legend_y = scene_rect.top()
        
        # 创建图例背景框
        legend_width = 180
        legend_height = 300
        background_rect = scene.addRect(legend_x - 10, legend_y - 10, 
                                      legend_width, legend_height,
                                      QPen(Qt.darkGray, 1), QBrush(QColor(248, 248, 248)))
        
        # 图例标题
        title = scene.addText("图例说明", QFont("SimHei", 11, QFont.Bold))
        title.setPos(legend_x + 5, legend_y)
        title.setDefaultTextColor(Qt.darkBlue)
        
        # 只显示实际存在的炮孔类型
        holes = self.design_area.get_blast_holes()
        existing_types = set()
        
        for hole in holes:
            # 检查特殊孔类型
            special_type = getattr(hole, 'special_hole_type', '普通孔')
            if special_type and special_type != '普通孔':
                existing_types.add(special_type)
            else:
                # 检查常规孔类型
                hole_type = getattr(hole, 'hole_type', '主炮孔')
                existing_types.add(hole_type)
        
        # 炮孔类型颜色映射
        type_colors = {
            "起始孔": QColor(255, 0, 0),
            "死孔": QColor(192, 192, 192),
            "失火孔": QColor(255, 255, 0),
            "普通孔": QColor(0, 0, 255),
            "主炮孔": QColor(255, 255, 0),
            "辅助孔": QColor(0, 255, 255),
            "缓冲孔": QColor(0, 255, 0),
            "周边孔": QColor(0, 0, 255),
            "保护孔": QColor(255, 0, 0)
        }
        
        # 显示实际存在的炮孔类型
        y_offset = 30
        current_y = legend_y + y_offset
        
        for hole_type in sorted(existing_types):
            if hole_type in type_colors:
                color = type_colors[hole_type]
                
                # 绘制示例圆圈
                scene.addEllipse(legend_x + 5, current_y, 16, 16, 
                               QPen(Qt.black, 1), QBrush(color))
                
                # 添加标签
                text = scene.addText(hole_type, QFont("SimHei", 9))
                text.setPos(legend_x + 28, current_y - 2)
                text.setDefaultTextColor(Qt.black)
                
                current_y += 22
        
        # 其他元素图例
        current_y += 10
        
        # 分隔线
        scene.addLine(legend_x, current_y, legend_x + 150, current_y, 
                     QPen(Qt.gray, 1))
        current_y += 15
        
        # 边界线
        if self._has_boundary_lines():
            scene.addLine(legend_x + 5, current_y + 8, legend_x + 20, current_y + 8, 
                         QPen(Qt.blue, 2, Qt.DashLine))
            boundary_text = scene.addText("边界线", QFont("SimHei", 9))
            boundary_text.setPos(legend_x + 28, current_y)
            boundary_text.setDefaultTextColor(Qt.black)
            current_y += 22
        
        # 作业面
        if self._has_work_surfaces():
            scene.addLine(legend_x + 5, current_y + 8, legend_x + 20, current_y + 8, 
                         QPen(Qt.red, 3, Qt.SolidLine))
            work_surface_text = scene.addText("作业面", QFont("SimHei", 9))
            work_surface_text.setPos(legend_x + 28, current_y)
            work_surface_text.setDefaultTextColor(Qt.black)
            current_y += 22
        
        # 参考点
        if self._has_reference_points():
            scene.addEllipse(legend_x + 8, current_y + 5, 8, 8, 
                           QPen(Qt.darkRed, 2), QBrush(Qt.red))
            ref_text = scene.addText("参考点", QFont("SimHei", 9))
            ref_text.setPos(legend_x + 28, current_y)
            ref_text.setDefaultTextColor(Qt.black)
            current_y += 22
        
        # 网格说明
        current_y += 10
        grid_text = scene.addText("网格间距: 50像素", QFont("SimHei", 8))
        grid_text.setPos(legend_x + 5, current_y)
        grid_text.setDefaultTextColor(Qt.darkGray)
    
    def _has_boundary_lines(self):
        """检查是否有边界线"""
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type') and item.item_type in ['boundary_line', 'boundary']:
                return True
        return False
    
    def _has_work_surfaces(self):
        """检查是否有作业面"""
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type') and item.item_type == 'work_surface':
                return True
        return False
    
    def _has_reference_points(self):
        """检查是否有参考点"""
        for item in self.design_area.graphics_scene.items():
            if hasattr(item, 'item_type') and item.item_type == 'reference_point':
                return True
            elif hasattr(item, 'rect') and item.rect().width() == 6 and not hasattr(item, 'item_type'):
                return True
        return False
    
    def _export_network_diagram(self, scene):
        """导出网络图"""
        from PyQt5.QtWidgets import QFileDialog
        from PyQt5.QtGui import QPainter
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出网络图", "", "PNG图片 (*.png);;JPEG图片 (*.jpg)")
        
        if file_path:
            from PyQt5.QtCore import QRectF
            from PyQt5.QtGui import QImage
            
            rect = scene.itemsBoundingRect()
            image = QImage(int(rect.width()) + 100, int(rect.height()) + 100, QImage.Format_ARGB32)
            image.fill(Qt.white)
            
            painter = QPainter(image)
            scene.render(painter, QRectF(), rect)
            painter.end()
            
            image.save(file_path)
            QMessageBox.information(self, "导出成功", f"网络图已保存到: {file_path}")
    
    def generate_parameter_table(self):
        """生成爆破参数表"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QTableWidget, QTableWidgetItem, 
                                    QPushButton, QHBoxLayout, QLabel, QCheckBox, QSplitter)
        from PyQt5.QtCore import Qt
        
        dialog = QDialog(self)
        dialog.setWindowTitle("爆破参数表")
        dialog.resize(1200, 700)
        
        main_layout = QVBoxLayout()
        
        # 添加说明标签
        info_label = QLabel("爆破参数表 - 详细的炮孔设计参数和计算结果")
        info_label.setStyleSheet("font-weight: bold; margin: 5px; color: blue;")
        main_layout.addWidget(info_label)
        
        # 创建分割器，左侧为表格，右侧为统计信息
        splitter = QSplitter(Qt.Horizontal)
        
        # 创建参数表
        table = self._create_parameter_table()
        splitter.addWidget(table)
        
        # 创建统计信息面板
        stats_widget = self._create_statistics_panel()
        splitter.addWidget(stats_widget)
        
        # 设置分割器比例
        splitter.setSizes([900, 300])
        main_layout.addWidget(splitter)
        
        # 添加按钮
        btn_layout = QHBoxLayout()
        
        # 导出选项
        export_csv_btn = QPushButton("导出CSV")
        export_excel_btn = QPushButton("导出Excel")
        export_pdf_btn = QPushButton("导出PDF")
        close_btn = QPushButton("关闭")
        
        # 设置按钮样式
        for btn in [export_csv_btn, export_excel_btn, export_pdf_btn, close_btn]:
            btn.setStyleSheet("QPushButton { padding: 8px 16px; }")
        
        export_csv_btn.clicked.connect(lambda: self._export_parameter_table(table, "csv"))
        export_excel_btn.clicked.connect(lambda: self._export_parameter_table(table, "excel"))
        export_pdf_btn.clicked.connect(lambda: self._export_parameter_table(table, "pdf"))
        close_btn.clicked.connect(dialog.close)
        
        btn_layout.addWidget(export_csv_btn)
        btn_layout.addWidget(export_excel_btn)
        btn_layout.addWidget(export_pdf_btn)
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        main_layout.addLayout(btn_layout)
        
        dialog.setLayout(main_layout)
        dialog.exec_()
        
        self.update_status("爆破参数表已生成")
    
    def _create_parameter_table(self):
        """创建参数表"""
        from PyQt5.QtWidgets import QTableWidget, QTableWidgetItem, QHeaderView
        from PyQt5.QtCore import Qt
        
        table = QTableWidget()
        holes = self.design_area.get_blast_holes()
        
        if holes:
            table.setRowCount(len(holes))
            table.setColumnCount(16)  # 增加到16列
            
            # 设置表头
            headers = [
                "序号", "炮孔编号", "X坐标(m)", "Y坐标(m)", "Z坐标(m)", 
                "孔类型", "设计孔深(m)", "孔距(m)", "排距(m)", "段高(m)",
                "炸药量(kg)", "单位长度装药量(kg/m)", "回填长度(m)", 
                "实测孔深(m)", "孔底标高(m)", "岩石类型"
            ]
            table.setHorizontalHeaderLabels(headers)
            
            # 填充数据
            for i, hole in enumerate(holes):
                # 序号
                table.setItem(i, 0, QTableWidgetItem(f"{i+1}"))
                
                # 炮孔编号
                hole_id = getattr(hole, 'hole_id', f'H{i+1:03d}')
                table.setItem(i, 1, QTableWidgetItem(hole_id))
                
                # 坐标
                x = hole.pos().x()
                y = hole.pos().y()
                z = getattr(hole, 'z_coord', self.blast_level)  # 使用Z坐标或爆破水平
                table.setItem(i, 2, QTableWidgetItem(f"{x:.2f}"))
                table.setItem(i, 3, QTableWidgetItem(f"{y:.2f}"))
                table.setItem(i, 4, QTableWidgetItem(f"{z:.2f}"))
                
                # 孔类型 - 优先显示特殊孔类型
                special_type = getattr(hole, 'special_hole_type', None)
                if special_type and special_type != '普通孔':
                    hole_type = special_type
                else:
                    hole_type = getattr(hole, 'hole_type', '主炮孔')
                table.setItem(i, 5, QTableWidgetItem(hole_type))
                
                # 设计孔深
                design_depth = getattr(hole, 'depth', 15.0)
                table.setItem(i, 6, QTableWidgetItem(f"{design_depth:.1f}"))
                
                # 孔距和排距
                spacing = getattr(hole, 'spacing', self.current_hole_spacing)
                row_spacing = getattr(hole, 'row_spacing', self.current_row_spacing)
                table.setItem(i, 7, QTableWidgetItem(f"{spacing:.1f}"))
                table.setItem(i, 8, QTableWidgetItem(f"{row_spacing:.1f}"))
                
                # 段高（使用台阶高度）
                step_height = getattr(self, 'step_height', 15.0)
                table.setItem(i, 9, QTableWidgetItem(f"{step_height:.1f}"))
                
                # 炸药量
                explosive_amount = getattr(hole, 'explosive_amount', 25.0)
                table.setItem(i, 10, QTableWidgetItem(f"{explosive_amount:.2f}"))
                
                # 单位长度装药量
                unit_charge = getattr(hole, 'unit_charge', 0.0)
                if unit_charge == 0.0 and hasattr(hole, 'charge_length'):
                    charge_length = getattr(hole, 'charge_length', design_depth)
                    if charge_length > 0:
                        unit_charge = explosive_amount / charge_length
                table.setItem(i, 11, QTableWidgetItem(f"{unit_charge:.2f}"))
                
                # 回填长度
                backfill_length = getattr(hole, 'backfill_length', 0.0)
                table.setItem(i, 12, QTableWidgetItem(f"{backfill_length:.1f}"))
                
                # 实测孔深（如果没有，使用设计孔深）
                measured_depth = getattr(hole, 'measured_depth', design_depth)
                table.setItem(i, 13, QTableWidgetItem(f"{measured_depth:.1f}"))
                
                # 孔底标高（Z坐标减去实测孔深）
                bottom_elevation = z - measured_depth
                table.setItem(i, 14, QTableWidgetItem(f"{bottom_elevation:.2f}"))
                
                # 岩石类型
                rock_type = getattr(hole, 'rock_type', '花岗岩')
                table.setItem(i, 15, QTableWidgetItem(rock_type))
            
            # 设置表格属性
            table.setSortingEnabled(True)  # 允许排序
            table.setAlternatingRowColors(True)  # 交替行颜色
            
            # 调整列宽
            header = table.horizontalHeader()
            for col in range(table.columnCount()):
                if col in [0, 1]:  # 序号和编号列
                    header.setSectionResizeMode(col, QHeaderView.ResizeToContents)
                elif col in [2, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14]:  # 数值列
                    table.setColumnWidth(col, 80)
                else:  # 文本列
                    table.setColumnWidth(col, 100)
        
        else:
            # 没有炮孔数据
            table.setRowCount(1)
            table.setColumnCount(1)
            table.setHorizontalHeaderLabels(["提示"])
            table.setItem(0, 0, QTableWidgetItem("暂无炮孔数据"))
        
        return table
    
    def _create_statistics_panel(self):
        """创建统计信息面板"""
        from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QFrame
        from PyQt5.QtCore import Qt
        
        widget = QWidget()
        layout = QVBoxLayout()
        
        # 标题
        title = QLabel("统计信息")
        title.setStyleSheet("font-weight: bold; font-size: 14px; color: blue; padding: 5px;")
        layout.addWidget(title)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
        
        # 计算统计数据
        holes = self.design_area.get_blast_holes()
        stats = self._calculate_statistics(holes)
        
        # 显示统计信息
        for label, value in stats.items():
            stat_label = QLabel(f"{label}: {value}")
            stat_label.setStyleSheet("padding: 3px; margin: 2px;")
            if "总计" in label or "平均" in label:
                stat_label.setStyleSheet("padding: 3px; margin: 2px; font-weight: bold; color: darkblue;")
            layout.addWidget(stat_label)
        
        # 添加弹性空间
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def _calculate_statistics(self, holes):
        """计算统计数据"""
        if not holes:
            return {"提示": "暂无数据"}
        
        stats = {}
        
        # 基本统计
        stats["炮孔总数"] = f"{len(holes)} 个"
        
        # 孔类型统计
        hole_types = {}
        special_types = {}
        total_explosive = 0
        total_depth = 0
        
        for hole in holes:
            # 统计特殊孔类型
            special_type = getattr(hole, 'special_hole_type', '普通孔')
            special_types[special_type] = special_types.get(special_type, 0) + 1
            
            # 统计常规孔类型
            hole_type = getattr(hole, 'hole_type', '主炮孔')
            hole_types[hole_type] = hole_types.get(hole_type, 0) + 1
            
            # 累计炸药量和孔深
            total_explosive += getattr(hole, 'explosive_amount', 25.0)
            total_depth += getattr(hole, 'depth', 15.0)
        
        # 显示特殊孔类型统计
        stats[""] = "--- 特殊孔类型 ---"
        for stype, count in special_types.items():
            if count > 0:
                stats[f"{stype}"] = f"{count} 个"
        
        # 显示常规孔类型统计
        stats[" "] = "--- 常规孔类型 ---"
        for htype, count in hole_types.items():
            if count > 0:
                stats[f"{htype}"] = f"{count} 个"
        
        # 炸药统计
        stats["  "] = "--- 炸药统计 ---"
        stats["总装药量"] = f"{total_explosive:.1f} kg"
        stats["平均装药量"] = f"{total_explosive/len(holes):.1f} kg/孔"
        
        # 孔深统计
        stats["   "] = "--- 孔深统计 ---"
        stats["总钻孔进尺"] = f"{total_depth:.1f} m"
        stats["平均孔深"] = f"{total_depth/len(holes):.1f} m"
        
        # 岩石类型统计
        rock_types = {}
        for hole in holes:
            rock_type = getattr(hole, 'rock_type', '花岗岩')
            rock_types[rock_type] = rock_types.get(rock_type, 0) + 1
        
        if len(rock_types) > 1:
            stats["    "] = "--- 岩石类型 ---"
            for rtype, count in rock_types.items():
                stats[f"{rtype}"] = f"{count} 个"
        
        return stats
    
    def _export_parameter_table(self, table, export_format="csv"):
        """导出参数表"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        
        # 根据格式设置文件对话框
        if export_format == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出CSV文件", f"爆破参数表_{self.design_name}.csv", "CSV文件 (*.csv)")
        elif export_format == "excel":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel文件", f"爆破参数表_{self.design_name}.xlsx", "Excel文件 (*.xlsx)")
        elif export_format == "pdf":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出PDF文件", f"爆破参数表_{self.design_name}.pdf", "PDF文件 (*.pdf)")
        else:
            return
        
        if file_path:
            try:
                if export_format == "csv":
                    self._export_to_csv(table, file_path)
                elif export_format == "excel":
                    self._export_to_excel(table, file_path)
                elif export_format == "pdf":
                    self._export_to_pdf(table, file_path)
                
                QMessageBox.information(self, "导出成功", f"参数表已保存到: {file_path}")
                
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"无法导出参数表: {str(e)}")
    
    def _export_to_csv(self, table, file_path):
        """导出为CSV格式"""
        import csv
        with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:  # 使用UTF-8-BOM编码
            writer = csv.writer(f)
            
            # 写入项目信息
            writer.writerow([f"项目名称: {self.design_name}"])
            writer.writerow([f"爆破水平: {self.blast_level}m"])
            writer.writerow([f"台阶高度: {self.step_height}m"])
            writer.writerow([""])  # 空行 
                    
            # 写入表头
            headers = []
            for col in range(table.columnCount()):
                headers.append(table.horizontalHeaderItem(col).text())
                writer.writerow(headers)
                    
                    # 写入数据
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else "")
                    writer.writerow(row_data)
                
    def _export_to_excel(self, table, file_path):
        """导出为Excel格式"""
        try:
            import pandas as pd
            
            # 准备数据
            data = []
            headers = []
            
            # 获取表头
            for col in range(table.columnCount()):
                headers.append(table.horizontalHeaderItem(col).text())
            
            # 获取数据
            for row in range(table.rowCount()):
                row_data = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    row_data.append(item.text() if item else "")
                data.append(row_data)
            
            # 创建DataFrame
            df = pd.DataFrame(data, columns=headers)
            
            # 导出到Excel
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 写入基本信息
                info_df = pd.DataFrame({
                    '项目信息': ['项目名称', '爆破水平', '台阶高度'],
                    '值': [self.design_name, f"{self.blast_level}m", f"{self.step_height}m"]
                })
                info_df.to_excel(writer, sheet_name='项目信息', index=False)
                
                # 写入参数表
                df.to_excel(writer, sheet_name='爆破参数表', index=False)
                
                # 格式化工作表
                workbook = writer.book
                worksheet = writer.sheets['爆破参数表']
                
                # 设置列宽
                for i, col in enumerate(headers):
                    if i in [0, 1]:  # 序号和编号列
                        worksheet.column_dimensions[chr(65+i)].width = 12
                    elif '坐标' in col or '孔深' in col or '量' in col:
                        worksheet.column_dimensions[chr(65+i)].width = 15
                    else:
                        worksheet.column_dimensions[chr(65+i)].width = 18
                
        except ImportError:
            # 如果没有pandas和openpyxl，使用简单的Excel格式（实际是CSV）
            import csv
            excel_path = file_path.replace('.xlsx', '.csv')
            self._export_to_csv(table, excel_path)
            QMessageBox.warning(self, "提示", 
                              "系统缺少Excel支持库，已导出为CSV格式。\n"
                              "要支持Excel格式，请安装: pip install pandas openpyxl")
    
    def _export_to_pdf(self, table, file_path):
        """导出为PDF格式"""
        try:
            from reportlab.lib.pagesizes import A4, landscape
            from reportlab.lib import colors
            from reportlab.lib.styles import getSampleStyleSheet
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # 注册中文字体（如果有的话）
            try:
                pdfmetrics.registerFont(TTFont('SimHei', 'SimHei.ttf'))
                font_name = 'SimHei'
            except:
                font_name = 'Helvetica'
            
            # 创建PDF文档
            doc = SimpleDocTemplate(file_path, pagesize=landscape(A4))
            story = []
            
            # 添加标题
            styles = getSampleStyleSheet()
            title_style = styles['Title']
            title_style.fontName = font_name
            
            title = Paragraph(f"爆破参数表 - {self.design_name}", title_style)
            story.append(title)
            story.append(Spacer(1, 20))
            
            # 准备表格数据
            table_data = []
            
            # 表头
            headers = []
            for col in range(table.columnCount()):
                headers.append(table.horizontalHeaderItem(col).text())
            table_data.append(headers)
            
            # 数据行
            for row in range(table.rowCount()):
                row_data = []
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    row_data.append(item.text() if item else "")
                table_data.append(row_data)
            
            # 创建表格
            pdf_table = Table(table_data)
            pdf_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), font_name),
                ('FONTSIZE', (0, 0), (-1, 0), 8),
                ('FONTNAME', (0, 1), (-1, -1), font_name),
                ('FONTSIZE', (0, 1), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(pdf_table)
            
            # 生成PDF
            doc.build(story)
            
        except ImportError:
            # 如果没有reportlab，使用简单的文本格式
            txt_path = file_path.replace('.pdf', '.txt')
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(f"爆破参数表 - {self.design_name}\n")
                f.write("=" * 50 + "\n\n")
                
                # 写入表头
                headers = []
                for col in range(table.columnCount()):
                    headers.append(table.horizontalHeaderItem(col).text())
                f.write("\t".join(headers) + "\n")
                f.write("-" * 50 + "\n")
                
                # 写入数据
                for row in range(table.rowCount()):
                    row_data = []
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        row_data.append(item.text() if item else "")
                    f.write("\t".join(row_data) + "\n")
            
            QMessageBox.warning(self, "提示", 
                              "系统缺少PDF支持库，已导出为文本格式。\n"
                              "要支持PDF格式，请安装: pip install reportlab")
    
    def open_file_manager(self):
        """打开本地文件管理器对话框"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QListWidget, 
                                    QListWidgetItem, QPushButton, QLabel, QFileDialog,
                                    QMessageBox, QInputDialog, QMenu, QAction)
        from PyQt5.QtCore import Qt, QDateTime
        import os
        import json
        import shutil
        
        dialog = QDialog(self)
        dialog.setWindowTitle("本地文件管理器")
        dialog.resize(600, 500)
        
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("本地设计文件管理")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; color: blue; padding: 5px;")
        layout.addWidget(title_label)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setContextMenuPolicy(Qt.CustomContextMenu)
        self.file_list.customContextMenuRequested.connect(lambda pos: self._show_file_context_menu(pos, dialog))
        self.file_list.itemDoubleClicked.connect(lambda item: self._open_selected_file(item, dialog))
        layout.addWidget(self.file_list)
        
        # 加载文件列表
        self._load_local_files()
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        
        # 文件操作按钮
        refresh_btn = QPushButton("刷新列表")
        open_btn = QPushButton("打开文件")
        rename_btn = QPushButton("重命名")
        delete_btn = QPushButton("删除文件")
        export_btn = QPushButton("导出文件")
        import_btn = QPushButton("导入文件")
        open_folder_btn = QPushButton("打开文件夹")
        close_btn = QPushButton("关闭")
        
        # 设置按钮样式
        for btn in [refresh_btn, open_btn, rename_btn, delete_btn, export_btn, import_btn, open_folder_btn, close_btn]:
            btn.setStyleSheet("QPushButton { padding: 6px 12px; }")
        
        # 连接信号
        refresh_btn.clicked.connect(self._load_local_files)
        open_btn.clicked.connect(lambda: self._open_selected_file(self.file_list.currentItem(), dialog))
        rename_btn.clicked.connect(self._rename_selected_file)
        delete_btn.clicked.connect(self._delete_selected_file)
        export_btn.clicked.connect(self._export_selected_file)
        import_btn.clicked.connect(self._import_file)
        open_folder_btn.clicked.connect(self._open_system_file_manager)
        close_btn.clicked.connect(dialog.close)
        
        btn_layout.addWidget(refresh_btn)
        btn_layout.addWidget(open_btn)
        btn_layout.addWidget(rename_btn)
        btn_layout.addWidget(delete_btn)
        btn_layout.addWidget(export_btn)
        btn_layout.addWidget(import_btn)
        btn_layout.addWidget(open_folder_btn)
        btn_layout.addStretch()
        btn_layout.addWidget(close_btn)
        
        layout.addLayout(btn_layout)
        
        dialog.setLayout(layout)
        dialog.exec_()
        
        self.update_status("本地文件管理器已打开")
    
    def _load_local_files(self):
        """加载本地文件列表"""
        import os
        import json
        from PyQt5.QtCore import QDateTime
        from PyQt5.QtWidgets import QListWidgetItem
        
        self.file_list.clear()
        
        # 获取当前目录下的所有.gerb和.json文件
        current_dir = os.getcwd()
        files = []
        
        for filename in os.listdir(current_dir):
            if filename.endswith(('.gerb', '.json')):
                file_path = os.path.join(current_dir, filename)
                if os.path.isfile(file_path):
                    # 获取文件信息
                    stat = os.stat(file_path)
                    size = stat.st_size
                    modified_time = QDateTime.fromSecsSinceEpoch(int(stat.st_mtime))
                    
                    # 尝试读取文件获取设计信息
                    design_name = "未知设计"
                    hole_count = 0
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if 'design_info' in data:
                                design_name = data['design_info'].get('name', '未知设计')
                            if 'holes' in data:
                                hole_count = len(data['holes'])
                    except:
                        pass  # 忽略读取错误
                    
                    files.append({
                        'filename': filename,
                        'path': file_path,
                        'design_name': design_name,
                        'hole_count': hole_count,
                        'size': size,
                        'modified': modified_time.toString('yyyy-MM-dd hh:mm:ss')
                    })
        
        # 按修改时间排序（最新的在前）
        files.sort(key=lambda x: x['modified'], reverse=True)
        
        # 添加到列表
        for file_info in files:
            display_text = f"{file_info['filename']}\n"
            display_text += f"设计名称: {file_info['design_name']}\n"
            display_text += f"炮孔数量: {file_info['hole_count']} 个\n"
            display_text += f"文件大小: {file_info['size']/1024:.1f} KB\n"
            display_text += f"修改时间: {file_info['modified']}"
            
            item = QListWidgetItem(display_text)
            item.setData(Qt.UserRole, file_info)  # 存储完整的文件信息
            self.file_list.addItem(item)
        
        if not files:
            item = QListWidgetItem("暂无设计文件")
            item.setData(Qt.UserRole, None)
            self.file_list.addItem(item)
    
    def _show_file_context_menu(self, pos, dialog):
        """显示文件右键菜单"""
        from PyQt5.QtWidgets import QMenu, QAction
        
        item = self.file_list.itemAt(pos)
        if not item or not item.data(Qt.UserRole):
            return
        
        menu = QMenu(dialog)
        
        open_action = QAction("打开文件", dialog)
        rename_action = QAction("重命名", dialog)
        delete_action = QAction("删除文件", dialog)
        export_action = QAction("导出到...", dialog)
        properties_action = QAction("属性", dialog)
        
        open_action.triggered.connect(lambda: self._open_selected_file(item, dialog))
        rename_action.triggered.connect(self._rename_selected_file)
        delete_action.triggered.connect(self._delete_selected_file)
        export_action.triggered.connect(self._export_selected_file)
        properties_action.triggered.connect(lambda: self._show_file_properties(item))
        
        menu.addAction(open_action)
        menu.addSeparator()
        menu.addAction(rename_action)
        menu.addAction(delete_action)
        menu.addSeparator()
        menu.addAction(export_action)
        menu.addAction(properties_action)
        
        menu.exec_(self.file_list.mapToGlobal(pos))
    
    def _open_selected_file(self, item, dialog):
        """打开选中的文件"""
        if not item or not item.data(Qt.UserRole):
            return
        
        file_info = item.data(Qt.UserRole)
        file_path = file_info['path']
        
        # 关闭文件管理器对话框
        dialog.close()
        
        # 打开文件
        try:
            self.current_file_path = file_path
            self._load_file_data(file_path)
            self.update_status(f"已打开文件: {file_info['filename']}")
        except Exception as e:
            QMessageBox.critical(self, "打开失败", f"无法打开文件: {str(e)}")
    
    def _load_file_data(self, file_path):
        """加载文件数据（复用现有的打开文件逻辑）"""
        import json
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 清空当前设计
        self.design_area.clear_design()
        
        # 加载设计参数
        if 'design_info' in data:
            design_info = data['design_info']
            self.design_name = design_info.get('name', '未命名设计')
            self.blast_level = design_info.get('blast_level', 0.0)
            self.step_height = design_info.get('step_height', 15.0)
        else:
            self.design_name = "导入的设计"
            self.blast_level = 0.0
            self.step_height = 15.0
        
        # 加载炮孔数据
        hole_count = 0
        if 'holes' in data:
            for hole_data in data['holes']:
                x = hole_data.get('x', 0)
                y = hole_data.get('y', 0)
                diameter = hole_data.get('diameter', 110)
                hole = self.design_area.add_blast_hole(x, y, diameter)
                hole.hole_id = hole_data.get('hole_id', f'H{hole_count+1:03d}')
                hole.depth = hole_data.get('depth', 15)
                hole.rock_type = hole_data.get('rock_type', '花岗岩')
                hole.explosive_type = hole_data.get('explosive_type', '2号岩石铵梯炸药')
                hole.explosive_amount = hole_data.get('explosive_amount', 25.0)
                hole.charge_structure = hole_data.get('charge_structure', '连续装药')
                hole.update_text_label()
                hole.update_rock_display()
                hole.update_explosive_display()
                hole_count += 1
        
        # 加载其他元素（边界线、作业面、文本、参考点等）
        # ... 这里可以复用现有的加载逻辑
        
        # 更新界面
        self.setWindowTitle(f"气能破岩参数设计云平台 - {self.design_name}")
        self.design_area.update_table_data()
        self.fit_view()
        self.design_area.save_state()  # 保存初始状态用于撤销重做
    
    def _rename_selected_file(self):
        """重命名选中的文件"""
        from PyQt5.QtWidgets import QInputDialog, QMessageBox
        import os
        
        item = self.file_list.currentItem()
        if not item or not item.data(Qt.UserRole):
            QMessageBox.warning(self, "提示", "请选择要重命名的文件")
            return
        
        file_info = item.data(Qt.UserRole)
        old_path = file_info['path']
        old_filename = file_info['filename']
        
        # 获取新文件名
        new_filename, ok = QInputDialog.getText(
            self, "重命名文件", "新文件名:", text=old_filename)
        
        if ok and new_filename and new_filename != old_filename:
            try:
                new_path = os.path.join(os.path.dirname(old_path), new_filename)
                os.rename(old_path, new_path)
                self._load_local_files()  # 刷新列表
                self.update_status(f"文件已重命名: {old_filename} -> {new_filename}")
            except Exception as e:
                QMessageBox.critical(self, "重命名失败", f"无法重命名文件: {str(e)}")
    
    def _delete_selected_file(self):
        """删除选中的文件"""
        from PyQt5.QtWidgets import QMessageBox
        import os
        
        item = self.file_list.currentItem()
        if not item or not item.data(Qt.UserRole):
            QMessageBox.warning(self, "提示", "请选择要删除的文件")
            return
        
        file_info = item.data(Qt.UserRole)
        filename = file_info['filename']
        file_path = file_info['path']
        
        # 确认删除
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除文件 '{filename}' 吗？\n此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                os.remove(file_path)
                self._load_local_files()  # 刷新列表
                self.update_status(f"文件已删除: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "删除失败", f"无法删除文件: {str(e)}")
    
    def _export_selected_file(self):
        """导出选中的文件到指定位置"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import shutil
        
        item = self.file_list.currentItem()
        if not item or not item.data(Qt.UserRole):
            QMessageBox.warning(self, "提示", "请选择要导出的文件")
            return
        
        file_info = item.data(Qt.UserRole)
        source_path = file_info['path']
        filename = file_info['filename']
        
        # 选择导出位置
        export_path, _ = QFileDialog.getSaveFileName(
            self, "导出文件", filename, "设计文件 (*.gerb);;JSON文件 (*.json);;所有文件 (*)")
        
        if export_path:
            try:
                shutil.copy2(source_path, export_path)
                QMessageBox.information(self, "导出成功", f"文件已导出到: {export_path}")
                self.update_status(f"文件已导出: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"无法导出文件: {str(e)}")
    
    def _import_file(self):
        """导入文件到本地"""
        from PyQt5.QtWidgets import QFileDialog, QMessageBox
        import shutil
        import os
        
        # 选择要导入的文件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择要导入的文件", "", "设计文件 (*.gerb);;JSON文件 (*.json);;所有文件 (*)")
        
        if file_path:
            try:
                filename = os.path.basename(file_path)
                destination = os.path.join(os.getcwd(), filename)
                
                # 检查文件是否已存在
                if os.path.exists(destination):
                    reply = QMessageBox.question(
                        self, "文件已存在", 
                        f"文件 '{filename}' 已存在，是否覆盖？",
                        QMessageBox.Yes | QMessageBox.No,
                        QMessageBox.No
                    )
                    if reply == QMessageBox.No:
                        return
                
                shutil.copy2(file_path, destination)
                self._load_local_files()  # 刷新列表
                QMessageBox.information(self, "导入成功", f"文件已导入: {filename}")
                self.update_status(f"文件已导入: {filename}")
            except Exception as e:
                QMessageBox.critical(self, "导入失败", f"无法导入文件: {str(e)}")
    
    def _show_file_properties(self, item):
        """显示文件属性"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton
        import os
        
        file_info = item.data(Qt.UserRole)
        
        dialog = QDialog(self)
        dialog.setWindowTitle(f"文件属性 - {file_info['filename']}")
        dialog.resize(400, 300)
        
        layout = QVBoxLayout()
        
        # 文件信息
        info_text = f"""文件名: {file_info['filename']}
设计名称: {file_info['design_name']}
炮孔数量: {file_info['hole_count']} 个
文件大小: {file_info['size']/1024:.1f} KB
文件路径: {file_info['path']}
修改时间: {file_info['modified']}"""
        
        info_label = QLabel(info_text)
        info_label.setStyleSheet("padding: 10px; background-color: #f5f5f5;")
        layout.addWidget(info_label)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)
        
        dialog.setLayout(layout)
        dialog.exec_()
    
    def _open_system_file_manager(self):
        """打开系统文件管理器"""
        import os
        import subprocess
        import platform
        
        try:
            current_dir = os.getcwd()
            system = platform.system()
            
            if system == "Windows":
                subprocess.Popen(f'explorer "{current_dir}"')
            elif system == "Darwin":  # macOS
                subprocess.Popen(["open", current_dir])
            else:  # Linux
                subprocess.Popen(["xdg-open", current_dir])
            
            self.update_status("已打开系统文件管理器")
            
        except Exception as e:
            QMessageBox.warning(self, "打开失败", f"无法打开文件管理器: {str(e)}")
    

    
    
    def show_view_config_dialog(self):
        """显示查看配置对话框 - 可勾选/取消显示项"""
        from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QCheckBox, 
                                    QPushButton, QHBoxLayout, QLabel, QGroupBox)
        
        dialog = QDialog(self)
        dialog.setWindowTitle("查看配置")
        dialog.setFixedSize(400, 500)
        
        layout = QVBoxLayout()
        
        # 说明标签
        info_label = QLabel("请选择要显示的信息项：")
        info_label.setStyleSheet("font-weight: bold; margin: 10px;")
        layout.addWidget(info_label)
        
        # 初始化显示配置（如果不存在）
        if not hasattr(self, 'view_config'):
            self.view_config = {
                'design_name': True,      # 设计名称
                'hole_id': True,          # 孔号
                'hole_depth': True,       # 孔深
                'spacing': True,          # 间距排距
                'work_surface': True,     # 作业面
                'boundary': True,         # 边界线
                'text': True,             # 文本
                'explosive_amount': True, # 炸药量
                'hole_diameter': True,    # 孔径
                'rock_type': True,        # 岩石
                'explosive_type': True,   # 炸药
                'hole_type': True,        # 孔类型
                'coordinates': True,      # 坐标
                'grid': True,             # 网格
                'connection_lines': True, # 连接线
            }
        
        # 基本信息组
        basic_group = QGroupBox("基本信息")
        basic_layout = QVBoxLayout()
        
        self.design_name_cb = QCheckBox("设计名称")
        self.design_name_cb.setChecked(self.view_config['design_name'])
        basic_layout.addWidget(self.design_name_cb)
        
        self.hole_id_cb = QCheckBox("孔号")
        self.hole_id_cb.setChecked(self.view_config['hole_id'])
        basic_layout.addWidget(self.hole_id_cb)
        
        self.coordinates_cb = QCheckBox("坐标")
        self.coordinates_cb.setChecked(self.view_config['coordinates'])
        basic_layout.addWidget(self.coordinates_cb)
        
        self.grid_cb = QCheckBox("网格")
        self.grid_cb.setChecked(self.view_config['grid'])
        basic_layout.addWidget(self.grid_cb)
        
        basic_group.setLayout(basic_layout)
        layout.addWidget(basic_group)
        
        # 炮孔参数组
        hole_group = QGroupBox("炮孔参数")
        hole_layout = QVBoxLayout()
        
        self.hole_depth_cb = QCheckBox("孔深")
        self.hole_depth_cb.setChecked(self.view_config['hole_depth'])
        hole_layout.addWidget(self.hole_depth_cb)
        
        self.hole_diameter_cb = QCheckBox("孔径")
        self.hole_diameter_cb.setChecked(self.view_config['hole_diameter'])
        hole_layout.addWidget(self.hole_diameter_cb)
        
        self.spacing_cb = QCheckBox("间距排距")
        self.spacing_cb.setChecked(self.view_config['spacing'])
        hole_layout.addWidget(self.spacing_cb)
        
        self.hole_type_cb = QCheckBox("孔类型")
        self.hole_type_cb.setChecked(self.view_config['hole_type'])
        hole_layout.addWidget(self.hole_type_cb)
        
        hole_group.setLayout(hole_layout)
        layout.addWidget(hole_group)
        
        # 材料参数组
        material_group = QGroupBox("材料参数")
        material_layout = QVBoxLayout()
        
        self.rock_type_cb = QCheckBox("岩石类型")
        self.rock_type_cb.setChecked(self.view_config['rock_type'])
        material_layout.addWidget(self.rock_type_cb)
        
        self.explosive_type_cb = QCheckBox("炸药类型")
        self.explosive_type_cb.setChecked(self.view_config['explosive_type'])
        material_layout.addWidget(self.explosive_type_cb)
        
        self.explosive_amount_cb = QCheckBox("炸药量")
        self.explosive_amount_cb.setChecked(self.view_config['explosive_amount'])
        material_layout.addWidget(self.explosive_amount_cb)
        
        material_group.setLayout(material_layout)
        layout.addWidget(material_group)
        
        # 图形元素组
        graphics_group = QGroupBox("图形元素")
        graphics_layout = QVBoxLayout()
        
        self.work_surface_cb = QCheckBox("作业面")
        self.work_surface_cb.setChecked(self.view_config['work_surface'])
        graphics_layout.addWidget(self.work_surface_cb)
        
        self.boundary_cb = QCheckBox("边界线")
        self.boundary_cb.setChecked(self.view_config['boundary'])
        graphics_layout.addWidget(self.boundary_cb)
        
        self.text_cb = QCheckBox("文本标注")
        self.text_cb.setChecked(self.view_config['text'])
        graphics_layout.addWidget(self.text_cb)
        
        self.connection_lines_cb = QCheckBox("连接线")
        self.connection_lines_cb.setChecked(self.view_config['connection_lines'])
        graphics_layout.addWidget(self.connection_lines_cb)
        
        graphics_group.setLayout(graphics_layout)
        layout.addWidget(graphics_group)
        
        # 按钮
        btn_layout = QHBoxLayout()
        
        apply_btn = QPushButton("应用")
        apply_btn.clicked.connect(lambda: self.apply_view_config(dialog))
        
        reset_btn = QPushButton("全选")
        reset_btn.clicked.connect(self.select_all_view_options)
        
        clear_btn = QPushButton("全不选")
        clear_btn.clicked.connect(self.clear_all_view_options)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(dialog.reject)
        
        btn_layout.addWidget(apply_btn)
        btn_layout.addWidget(reset_btn)
        btn_layout.addWidget(clear_btn)
        btn_layout.addWidget(cancel_btn)
        
        layout.addLayout(btn_layout)
        dialog.setLayout(layout)
        
        # 保存对话框引用以便其他方法访问复选框
        self.view_config_dialog = dialog
        
        dialog.exec_()
    
    def select_all_view_options(self):
        """全选所有显示选项"""
        if hasattr(self, 'view_config_dialog'):
            checkboxes = self.view_config_dialog.findChildren(QCheckBox)
            for cb in checkboxes:
                cb.setChecked(True)
    
    def clear_all_view_options(self):
        """清除所有显示选项"""
        if hasattr(self, 'view_config_dialog'):
            checkboxes = self.view_config_dialog.findChildren(QCheckBox)
            for cb in checkboxes:
                cb.setChecked(False)
    
    def apply_view_config(self, dialog):
        """应用显示配置"""
        # 更新配置
        self.view_config = {
            'design_name': self.design_name_cb.isChecked(),
            'hole_id': self.hole_id_cb.isChecked(),
            'hole_depth': self.hole_depth_cb.isChecked(),
            'spacing': self.spacing_cb.isChecked(),
            'work_surface': self.work_surface_cb.isChecked(),
            'boundary': self.boundary_cb.isChecked(),
            'text': self.text_cb.isChecked(),
            'explosive_amount': self.explosive_amount_cb.isChecked(),
            'hole_diameter': self.hole_diameter_cb.isChecked(),
            'rock_type': self.rock_type_cb.isChecked(),
            'explosive_type': self.explosive_type_cb.isChecked(),
            'hole_type': self.hole_type_cb.isChecked(),
            'coordinates': self.coordinates_cb.isChecked(),
            'grid': self.grid_cb.isChecked(),
            'connection_lines': self.connection_lines_cb.isChecked(),
        }
        
        # 应用显示设置到设计区域
        self.apply_display_settings()
        
        # 更新状态
        enabled_count = sum(1 for enabled in self.view_config.values() if enabled)
        self.update_status(f"已应用显示配置，启用了 {enabled_count} 个显示项")
        
        dialog.accept()
    
    def apply_display_settings(self):
        """将显示配置应用到设计区域"""
        try:
            # 更新所有炮孔的显示
            holes = self.design_area.get_blast_holes()
            for hole in holes:
                # 更新炮孔标签显示
                self.update_hole_label_display(hole)
                
                # 更新炮孔的可见性（根据配置）
                hole.setVisible(True)  # 炮孔本身始终可见
            
            # 更新其他图形元素的显示
            for item in self.design_area.graphics_scene.items():
                if hasattr(item, 'item_type'):
                    # 作业面
                    if item.item_type == 'work_surface':
                        item.setVisible(self.view_config.get('work_surface', True))
                    
                    # 边界线
                    elif item.item_type in ['boundary', 'boundary_line']:
                        item.setVisible(self.view_config.get('boundary', True))
                    
                    # 文本标注
                    elif item.item_type == 'text_annotation':
                        item.setVisible(self.view_config.get('text', True))
                    
                    # 连接线
                    elif item.item_type == 'connection_line':
                        item.setVisible(self.view_config.get('connection_lines', True))
            
            # 更新网格显示
            self.update_grid_visibility()
            
            # 强制刷新设计区域
            self.design_area.graphics_scene.update()
            if hasattr(self.design_area, 'graphics_view'):
                self.design_area.graphics_view.update()
            
            print(f"显示配置已应用，启用项目: {[k for k, v in self.view_config.items() if v]}")
            
        except Exception as e:
            print(f"应用显示设置时出错: {e}")
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.warning(self, "错误", f"应用显示设置失败: {str(e)}")
    
    def update_hole_label_display(self, hole):
        """更新单个炮孔的标签显示"""
        try:
            # 确保炮孔有text_item属性（实际属性名）
            if not hasattr(hole, 'text_item') or hole.text_item is None:
                return
            
            label_parts = []
            
            # 孔号 - 始终显示
            if self.view_config.get('hole_id', True) and hasattr(hole, 'hole_id'):
                label_parts.append(f"{hole.hole_id}")
            
            # 坐标
            if self.view_config.get('coordinates', False):
                x, y = hole.pos().x(), hole.pos().y()
                label_parts.append(f"({x:.1f},{y:.1f})")
            
            # 孔深
            if self.view_config.get('hole_depth', True):
                depth = getattr(hole, 'depth', 15)
                label_parts.append(f"深度{depth}m")
            
            # 孔径
            if self.view_config.get('hole_diameter', True):
                diameter = getattr(hole, 'diameter_value', 110)
                label_parts.append(f"φ{diameter}")
            
            # 间距排距
            if self.view_config.get('spacing', False):
                spacing = getattr(hole, 'spacing', 3.5)
                row_spacing = getattr(hole, 'row_spacing', 4.0)
                label_parts.append(f"{spacing}×{row_spacing}m")
            
            # 孔类型
            if self.view_config.get('hole_type', False):
                hole_type = getattr(hole, 'hole_type', '主炮孔')
                label_parts.append(f"{hole_type}")
            
            # 岩石类型
            if self.view_config.get('rock_type', False):
                rock_type = getattr(hole, 'rock_type', '花岗岩')
                label_parts.append(f"{rock_type}")
            
            # 炸药类型
            if self.view_config.get('explosive_type', False):
                explosive_type = getattr(hole, 'explosive_type', '2号岩石铵梯炸药')
                # 简化炸药名称显示
                short_name = explosive_type.replace('2号岩石铵梯炸药', '铵梯').replace('乳化炸药', '乳化')
                label_parts.append(f"{short_name}")
            
            # 炸药量
            if self.view_config.get('explosive_amount', False):
                explosive_amount = getattr(hole, 'explosive_amount', 25.0)
                label_parts.append(f"{explosive_amount}kg")
            
            # 更新标签文本
            if label_parts:
                new_text = '\n'.join(label_parts)
            else:
                new_text = getattr(hole, 'hole_id', 'H1')
            
            hole.text_item.setPlainText(new_text)
            
            # 确保复合标签显示在炮孔右侧
            hole.text_item.setPos(25, -25)
            
            # 强制更新标签显示
            hole.text_item.update()
            
        except Exception as e:
            print(f"更新炮孔 {getattr(hole, 'hole_id', 'unknown')} 标签显示时出错: {e}")
    
    def toggle_move_mode(self):
        """切换移动模式和选择模式"""
        if self.design_area.get_current_mode() == "move":
            self.design_area.set_mode("select")
            self.update_status("已切换到选择模式")
        else:
            self.design_area.set_mode("move")
            self.update_status("已激活移动模式")
    
    def toggle_zoom_mode(self):
        """切换缩放模式和选择模式"""
        if self.design_area.get_current_mode() == "zoom":
            self.design_area.set_mode("select")
            self.update_status("已切换到选择模式")
        else:
            self.design_area.set_mode("zoom")
            self.update_status("已激活缩放模式")
    
    def zoom_in(self):
        """放大"""
        self.design_area.zoom_in()
        self.update_status("放大视图")
    
    def zoom_out(self):
        """缩小"""
        self.design_area.zoom_out()
        self.update_status("缩小视图")
    
    def fit_view(self):
        """适应视图"""
        self.design_area.fit_view()
        self.update_status("适应全图")
    
    def show_intelligent_layout(self):
        """显示智能布孔对话框"""
        try:
            from .dialogs.intelligent_layout_dialog import IntelligentLayoutDialog
            
            dialog = IntelligentLayoutDialog(self)
            dialog.layout_calculated.connect(self.apply_intelligent_layout)
            dialog.exec_()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开智能布孔对话框失败: {e}")
    
    def apply_intelligent_layout(self, layout_data):
        """应用智能布孔设计"""
        try:
            input_params = layout_data['input_params']
            results = layout_data['results']
            adjusted_params = layout_data.get('adjusted_params', {})
            
            # 获取设计参数
            layout_type = input_params['layout_type']
            hole_spacing = adjusted_params.get('hole_spacing', results['hole_spacing'])
            row_spacing = adjusted_params.get('row_spacing', results['row_spacing'])
            diameter = input_params['diameter']
            explosive_amount = adjusted_params.get('explosive_amount', results['explosive_amount'])
            
            # 启用智能布孔交互模式
            if layout_type == '一排':
                self.start_single_row_layout(layout_data)
            else:
                self.start_blast_area_layout(layout_data)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用智能布孔失败: {e}")
    
    def start_single_row_layout(self, layout_data):
        """开始一排布孔交互"""
        self.current_layout_data = layout_data
        
        # 直接创建一排布孔，不需要用户交互
        self.create_fixed_single_row_layout(layout_data)
    
    def create_fixed_single_row_layout(self, layout_data):
        """创建固定一排布孔"""
        try:
            print(f"DEBUG: create_fixed_single_row_layout 开始")
            
            input_params = layout_data['input_params']
            results = layout_data['results']
            adjusted_params = layout_data.get('adjusted_params', {})
            
            # 获取参数
            hole_spacing = adjusted_params.get('hole_spacing', results['hole_spacing'])
            diameter = input_params['diameter']
            explosive_amount = adjusted_params.get('explosive_amount', results['explosive_amount'])
            
            print(f"DEBUG: 参数 - 孔距: {hole_spacing}, 孔径: {diameter}, 炸药量: {explosive_amount}")
            
            # 检查是否有预设坐标
            points = adjusted_params.get('points', [])
            if len(points) >= 2 and points[0] != (0, 0) and points[1] != (0, 0):
                # 使用用户设置的坐标
                start_x, start_y = points[0]
                end_x, end_y = points[1]
                
                # 计算布孔方向
                dx = end_x - start_x
                dy = end_y - start_y
                distance = math.sqrt(dx*dx + dy*dy)
                
                if distance > 0:
                    # 计算炮孔数量
                    pixel_spacing = hole_spacing * 50  # 转换为像素单位
                    hole_count = max(2, int(distance / pixel_spacing) + 1)
                    
                    # 生成炮孔位置
                    holes_to_add = []
                    for i in range(hole_count):
                        t = i / (hole_count - 1) if hole_count > 1 else 0
                        x = start_x + t * dx
                        y = start_y + t * dy
                        holes_to_add.append((x, y, diameter, explosive_amount))
                    
                    print(f"DEBUG: 使用预设坐标，炮孔数: {hole_count}")
                else:
                    # 距离为0，使用默认中心位置
                    self._create_default_single_row_layout(hole_spacing, diameter, explosive_amount)
                    return
            else:
                # 使用默认中心位置
                self._create_default_single_row_layout(hole_spacing, diameter, explosive_amount)
                return
            
            print(f"DEBUG: 准备添加 {len(holes_to_add)} 个炮孔")
            
            # 添加炮孔到设计区域
            added_count = 0
            for i, (x, y, d, exp_amount) in enumerate(holes_to_add):
                # 使用统一的炮孔大小 (20像素)
                hole = self.design_area.add_blast_hole(x, y, 20)
                if hole:
                    # 设置炮孔参数
                    hole.rock_type = input_params['rock_type']
                    hole.explosive_type = input_params['explosive_type']
                    hole.depth = input_params['design_depth']
                    hole.explosive_amount = exp_amount
                    hole.diameter_value = d  # 设置实际孔径值
                    
                    # 明确设置为主炮孔类型
                    hole.hole_type = "主炮孔"
                    
                    # 一排布孔使用黄色显示
                    hole.setBrush(QBrush(Qt.yellow))
                    hole.layout_mode = '一排'
                    
                    # 设置AVV参数
                    hole.spacing = hole_spacing
                    hole.row_spacing = hole_spacing * 0.7  # 一排布孔排距为孔距的0.7倍
                    
                    # 更新所有显示
                    hole.update_text_label()
                    hole.update_rock_display()
                    added_count += 1
                    
                    print(f"DEBUG: 添加炮孔 {i+1}: {hole.hole_id} 位置({x:.1f}, {y:.1f})")
            
            print(f"DEBUG: 实际添加炮孔数: {added_count}")
            
            # 更新设计区域
            self.design_area.update()
            self.design_area.update_table_data()
            
            # 保存状态用于撤销重做

            
            # 自动适应视图
            self.fit_view()
            
            self.update_status(f"智能一排布孔完成，添加了 {added_count} 个炮孔")
            
            # 显示布孔结果摘要
            self.show_layout_summary(layout_data, added_count)
            
            print(f"DEBUG: create_fixed_single_row_layout 完成")
            
        except Exception as e:
            print(f"DEBUG: create_fixed_single_row_layout 异常: {e}")
            QMessageBox.critical(self, "错误", f"创建固定一排布孔失败: {e}")
    
    def _create_default_single_row_layout(self, hole_spacing, diameter, explosive_amount):
        """创建默认一排布孔（使用中心位置）"""
        try:
            # 计算布孔区域中心 - 创建一条水平线
            scene_rect = self.design_area.graphics_scene.itemsBoundingRect()
            if scene_rect.isEmpty():
                center_x = 400
                center_y = 300
            else:
                center_x = scene_rect.center().x()
                center_y = scene_rect.center().y()
            
            # 一排布孔的默认参数
            line_length = 200  # 默认线长200像素
            hole_count = max(2, int(line_length / (hole_spacing * 50))) + 1  # 至少2个孔
            
            print(f"DEBUG: 默认布孔中心: ({center_x}, {center_y}), 炮孔数: {hole_count}")
            
            # 生成一排炮孔位置
            holes_to_add = []
            pixel_spacing = hole_spacing * 50  # 转换为像素单位
            start_x = center_x - (hole_count - 1) * pixel_spacing / 2
            
            for i in range(hole_count):
                x = start_x + i * pixel_spacing
                y = center_y
                holes_to_add.append((x, y, diameter, explosive_amount))
            
            print(f"DEBUG: 准备添加 {len(holes_to_add)} 个炮孔")
            
            # 添加炮孔到设计区域
            added_count = 0
            for i, (x, y, d, exp_amount) in enumerate(holes_to_add):
                # 使用统一的炮孔大小 (20像素)
                hole = self.design_area.add_blast_hole(x, y, 20)
                if hole:
                    # 设置炮孔参数
                    hole.rock_type = self.current_layout_data['input_params']['rock_type']
                    hole.explosive_type = self.current_layout_data['input_params']['explosive_type']
                    hole.depth = self.current_layout_data['input_params']['design_depth']
                    hole.explosive_amount = exp_amount
                    hole.diameter_value = d  # 设置实际孔径值
                    
                    # 明确设置为主炮孔类型
                    hole.hole_type = "主炮孔"
                    
                    # 一排布孔使用黄色显示
                    hole.setBrush(QBrush(Qt.yellow))
                    hole.layout_mode = '一排'
                    
                    # 设置AVV参数
                    hole.spacing = hole_spacing
                    hole.row_spacing = hole_spacing * 0.7  # 一排布孔排距为孔距的0.7倍
                    
                    # 更新所有显示
                    hole.update_text_label()
                    hole.update_rock_display()
                    added_count += 1
                    
                    print(f"DEBUG: 添加炮孔 {i+1}: {hole.hole_id} 位置({x:.1f}, {y:.1f})")
            
            print(f"DEBUG: 实际添加炮孔数: {added_count}")
            
            # 更新设计区域
            self.design_area.update()
            self.design_area.update_table_data()
            
            # 自动适应视图
            self.fit_view()
            
            self.update_status(f"智能一排布孔完成，添加了 {added_count} 个炮孔")
            
            # 显示布孔结果摘要
            self.show_layout_summary(self.current_layout_data, added_count)
            
        except Exception as e:
            print(f"DEBUG: _create_default_single_row_layout 异常: {e}")
            QMessageBox.critical(self, "错误", f"创建默认一排布孔失败: {e}")
    
    def start_blast_area_layout(self, layout_data):
        """开始爆区布孔交互"""
        self.current_layout_data = layout_data
        adjusted_params = layout_data.get('adjusted_params', {})
        
        # 如果有排数和每排孔数设置，使用固定布孔
        if 'row_count' in adjusted_params and 'holes_per_row' in adjusted_params:
            self.create_fixed_blast_area_layout(layout_data)
        else:
            # 否则使用交互式布孔
            self.design_area.set_layout_mode("blast_area", layout_data)
            self.update_status("请在设计图上拖动鼠标选定平行四边形布孔区域，或按ESC取消")
    
    def create_fixed_blast_area_layout(self, layout_data):
        """创建固定爆区布孔"""
        try:
            input_params = layout_data['input_params']
            results = layout_data['results']
            adjusted_params = layout_data.get('adjusted_params', {})
            
            # 获取参数
            hole_spacing = adjusted_params.get('hole_spacing', results['hole_spacing'])
            row_spacing = adjusted_params.get('row_spacing', results['row_spacing'])
            diameter = input_params['diameter']
            explosive_amount = adjusted_params.get('explosive_amount', results['explosive_amount'])
            row_count = adjusted_params.get('row_count', 3)
            holes_per_row = adjusted_params.get('holes_per_row', 5)
            layout_mode = input_params.get('layout_mode', '矩形布孔')
            
            # 检查是否有预设坐标
            points = adjusted_params.get('points', [])
            if len(points) >= 4 and all(p != (0, 0) for p in points):
                # 使用用户设置的四个角点坐标
                p1_x, p1_y = points[0]
                p2_x, p2_y = points[1]
                p3_x, p3_y = points[2]
                p4_x, p4_y = points[3]
                
                # 计算布孔区域
                center_x = (p1_x + p2_x + p3_x + p4_x) / 4
                center_y = (p1_y + p2_y + p3_y + p4_y) / 4
                
                # 计算区域大小
                width = max(abs(p2_x - p1_x), abs(p3_x - p4_x))
                height = max(abs(p3_y - p1_y), abs(p4_y - p2_y))
                
                print(f"DEBUG: 使用预设坐标，中心: ({center_x}, {center_y}), 区域: {width}x{height}")
            else:
                # 使用默认中心位置
                scene_rect = self.design_area.graphics_scene.itemsBoundingRect()
                if scene_rect.isEmpty():
                    center_x = 400
                    center_y = 300
                else:
                    center_x = scene_rect.center().x()
                    center_y = scene_rect.center().y()
                
                print(f"DEBUG: 使用默认中心: ({center_x}, {center_y})")
            
            # 生成炮孔位置
            holes_to_add = []
            pixel_spacing = hole_spacing * 50  # 转换为像素单位
            pixel_row_spacing = row_spacing * 50
            
            if layout_mode == '矩形布孔':
                # 矩形布孔
                for row in range(row_count):
                    for col in range(holes_per_row):
                        x = center_x + (col - holes_per_row//2) * pixel_spacing
                        y = center_y + (row - row_count//2) * pixel_row_spacing
                        holes_to_add.append((x, y, diameter, explosive_amount))
            
            else:  # 三角形布孔
                # 三角形布孔：交错排列
                for row in range(row_count):
                    # 奇数行（索引从0开始）炮孔向右偏移半个孔距
                    offset = (pixel_spacing / 2) if row % 2 == 1 else 0
                    
                    for col in range(holes_per_row):
                        x = center_x + (col - holes_per_row//2) * pixel_spacing + offset
                        y = center_y + (row - row_count//2) * pixel_row_spacing
                        holes_to_add.append((x, y, diameter, explosive_amount))
            
            # 添加炮孔到设计区域
            added_count = 0
            for i, (x, y, d, exp_amount) in enumerate(holes_to_add):
                # 使用统一的炮孔大小 (20像素)
                hole = self.design_area.add_blast_hole(x, y, 20)
                if hole:
                    # 设置炮孔参数
                    hole.rock_type = input_params['rock_type']
                    hole.explosive_type = input_params['explosive_type']
                    hole.depth = input_params['design_depth']
                    hole.explosive_amount = exp_amount
                    hole.diameter_value = d  # 设置实际孔径值
                    
                    # 明确设置为主炮孔类型
                    hole.hole_type = "主炮孔"
                    
                    # 根据布孔模式设置炮孔样式
                    if layout_mode == '矩形布孔':
                        hole.setBrush(QBrush(Qt.yellow))  # 矩形布孔用黄色
                        hole.layout_mode = '矩形'
                    else:  # 三角形布孔
                        hole.setBrush(QBrush(QColor(255, 165, 0)))  # 三角形布孔用橙色
                        hole.layout_mode = '三角形'
                    
                    # 设置AVV参数
                    hole.spacing = hole_spacing
                    hole.row_spacing = row_spacing
                    
                    # 更新所有显示
                    hole.update_text_label()
                    hole.update_rock_display()
                    added_count += 1
            
            # 更新设计区域
            self.design_area.update()
            self.design_area.update_table_data()
            
            # 保存状态用于撤销重做

            
            # 自动适应视图
            self.fit_view()
            
            self.update_status(f"智能布孔完成，添加了 {added_count} 个炮孔")
            
            # 显示布孔结果摘要
            self.show_layout_summary(layout_data, added_count)
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建固定爆区布孔失败: {e}")
    
    def show_layout_summary(self, layout_data, added_count):
        """显示布孔结果摘要"""
        input_params = layout_data['input_params']
        results = layout_data['results']
        adjusted_params = layout_data.get('adjusted_params', {})
        
        layout_type = input_params['layout_type']
        hole_spacing = adjusted_params.get('hole_spacing', results['hole_spacing'])
        row_spacing = adjusted_params.get('row_spacing', results['row_spacing'])
        explosive_amount = adjusted_params.get('explosive_amount', results['explosive_amount'])
        
        layout_mode = input_params.get('layout_mode', '矩形布孔')
        
        if layout_type == "一排":
            summary = f"""智能一排布孔完成！

布孔参数:
- 布孔类型: {layout_type}
- 孔距: {hole_spacing:.2f} m
- 孔径: {input_params['diameter']:.0f} mm
- 设计孔深: {input_params['design_depth']:.1f} m
- 回填长度: {input_params['backfill_length']:.1f} m
- 岩石类型: {input_params['rock_type']}
- 炸药类型: {input_params['explosive_type']}

计算结果:
- 炸药量: {explosive_amount:.2f} kg/孔
- 单位长度装药量: {results['unit_charge']:.2f} kg/m
- 最小填塞长度: {results['min_stemming']:.2f} m
- 最大装药能力: {results['max_charge_capacity']:.2f} kg

实际添加炮孔数量: {added_count} (蓝色显示)

布孔说明:
程序按设定的孔距在水平线上均匀分布炮孔，可通过拖拽调整位置。"""
        else:
            summary = f"""智能布孔完成！

布孔参数:
- 布孔类型: {layout_type}
- 布孔模式: {layout_mode}
- 孔距: {hole_spacing:.2f} m
- 排距: {row_spacing:.2f} m
- 孔径: {input_params['diameter']:.0f} mm
- 设计孔深: {input_params['design_depth']:.1f} m
- 回填长度: {input_params['backfill_length']:.1f} m
- 岩石类型: {input_params['rock_type']}
- 炸药类型: {input_params['explosive_type']}

计算结果:
- 炸药量: {explosive_amount:.2f} kg/孔
- 单位长度装药量: {results['unit_charge']:.2f} kg/m
- 最小填塞长度: {results['min_stemming']:.2f} m
- 最大装药能力: {results['max_charge_capacity']:.2f} kg

布孔模式说明:
- 矩形布孔: 炮孔按正方形网格排列 (黄色显示)
- 三角形布孔: 炮孔按三角形网格交错排列 (橙色显示)

实际添加炮孔数量: {added_count}"""
            
            if layout_type == "爆区" and 'row_count' in adjusted_params:
                summary += f"\n\n布孔设置:\n- 排数: {adjusted_params['row_count']}\n- 每排孔数: {adjusted_params['holes_per_row']}"
        
        QMessageBox.information(self, "智能布孔完成", summary)
    
    def switch_to_graphics_view(self):
        """切换到图形视图"""
        self.design_area.set_mode("graphics")
        self.update_status("已切换到图形视图")
    
    def switch_to_table_view(self):
        """切换到表格视图"""
        self.design_area.set_mode("table")
        self.update_status("已切换到表格视图")
    
    def toggle_toolbar(self):
        """切换工具栏显示状态"""
        if self.toolbar_widget.isVisible():
            self.toolbar_widget.hide()
            self.update_status("已隐藏工具栏")
        else:
            self.toolbar_widget.show()
            self.update_status("已显示工具栏")
    
    def toggle_statusbar(self):
        """切换状态栏显示状态"""
        if self.status_bar.isVisible():
            self.status_bar.hide()
        else:
            self.status_bar.show()
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        from PyQt5.QtCore import Qt
        

        
        # Ctrl+S 保存
        if event.key() == Qt.Key_S and event.modifiers() == Qt.ControlModifier:
            self.save_file()
            return
        
        # Ctrl+O 打开
        if event.key() == Qt.Key_O and event.modifiers() == Qt.ControlModifier:
            self.open_file()
            return
        
        # Ctrl+N 新建
        if event.key() == Qt.Key_N and event.modifiers() == Qt.ControlModifier:
            self.new_file()
            return
        
        # ESC键处理
        if event.key() == Qt.Key_Escape:
            # ESC键退出各种特殊模式
            # 优先检查绘制模式
            if hasattr(self.design_area.graphics_view, 'drawing_mode') and self.design_area.graphics_view.drawing_mode:
                self.design_area.graphics_view.exit_drawing_mode()
                self.update_status("已退出绘制模式")
            elif hasattr(self.design_area, 'special_hole_mode') and self.design_area.special_hole_mode:
                self.design_area.exit_fine_design_mode()
            elif hasattr(self.design_area, 'layout_mode') and self.design_area.layout_mode:
                self.design_area.exit_layout_mode()
            else:
                # 退出其他操作模式
                self.design_area.exit_hole_operation_mode()
        else:
            super().keyPressEvent(event)
    
    def closeEvent(self, event):
        """关闭事件处理"""
        reply = QMessageBox.question(self, '确认退出', '确定要退出程序吗？',
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.No)
        if reply == QMessageBox.Yes:
            event.accept()
        else:
            event.ignore()
    
    def set_special_hole_type(self, hole_type):
        """设置特殊孔类型（起爆孔）"""
        from PyQt5.QtWidgets import QMessageBox
        
        self.current_special_hole_type = hole_type
        
        if hole_type == "所有普通孔":
            # 设置所有炮孔为普通孔
            holes = self.design_area.get_blast_holes()
            if not holes:
                QMessageBox.information(self, "提示", "当前没有炮孔！")
                return
            
            updated_count = 0
            for hole in holes:
                hole.special_hole_type = "普通孔"
                hole.update_hole_color()
                # 同时更新炸药信息为当前配置
                hole.explosive_type = self.current_explosive_type
                hole.charge_structure = getattr(self, 'current_charge_structure', '连续装药')
                # 更新炸药相关标签显示
                if hasattr(hole, 'update_explosive_display'):
                    hole.update_explosive_display()
                updated_count += 1
            
            self.design_area.update_table_data()
            self.design_area.save_state()
            self.update_status(f"已将所有 {updated_count} 个炮孔设置为普通孔")
        else:
            # 进入特殊孔设置模式
            self.design_area.graphics_view.setCursor(Qt.CrossCursor)
            self.update_status(f"请点击要设置为{hole_type}的炮孔，按ESC退出设置模式")
            self.design_area.special_hole_mode = hole_type
    
    def set_drilling_equipment(self):
        """为选中的炮孔设置打孔设备和孔径"""
        from PyQt5.QtWidgets import QMessageBox
        
        selected_items = self.design_area.graphics_scene.selectedItems()
        hole_items = [item for item in selected_items if hasattr(item, 'drilling_equipment')]
        
        if not hole_items:
            QMessageBox.warning(self, "提示", "请先选择要设置打孔设备的炮孔！")
            return
            
        # 使用新的专用对话框类
        dialog = AssignDrillingEquipmentDialog(self.drilling_equipment, self)
        
        # 进入孔径设置的特殊显示模式
        self.design_area.start_diameter_setting_mode()

        if dialog.exec_() == QDialog.Accepted:
            selection = dialog.get_selection()
            selected_equipment_name = selection['equipment_name']
            selected_diameter = selection['diameter']
            
            updated_count = 0
            for hole in hole_items:
                hole.drilling_equipment = selected_equipment_name
                hole.equipment_diameter = selected_diameter
                hole.diameter_value = selected_diameter
                hole.update_diameter(selected_diameter)
                hole.update_text_label()
                updated_count += 1
            
            self.design_area.save_state()
            self.update_status(f"已为 {updated_count} 个炮孔设置设备: {selected_equipment_name}")
        
        # 退出特殊显示模式
        self.design_area.exit_diameter_setting_mode()

    def set_all_holes_diameter(self):
        """为所有炮孔设置孔径"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QDoubleSpinBox, QPushButton, QMessageBox, QComboBox, QFormLayout
        
        holes = self.design_area.get_blast_holes()
        if not holes:
            QMessageBox.information(self, "提示", "当前没有炮孔！")
            return
        
        dialog = QDialog(self)
        dialog.setWindowTitle("设置所有孔径")
        dialog.resize(400, 200)
        
        layout = QVBoxLayout()
        
        # 设备选择
        equipment_layout = QHBoxLayout()
        equipment_layout.addWidget(QLabel("打孔设备:"))
        equipment_combo = QComboBox()
        
        equipment_list = getattr(self, 'drilling_equipment', [
            "YT27手风钻", "YT24气腿式风钻", "7655潜孔钻机", 
            "KQG-150潜孔钻机", "CM351履带钻机"
        ])
        equipment_combo.addItems(equipment_list)
        equipment_layout.addWidget(equipment_combo)
        layout.addLayout(equipment_layout)
        
        # 孔径设置
        diameter_layout = QHBoxLayout()
        diameter_layout.addWidget(QLabel("孔径(mm):"))
        diameter_spin = QDoubleSpinBox()
        diameter_spin.setRange(50, 300)
        diameter_spin.setValue(110)
        diameter_spin.setSingleStep(1)
        diameter_layout.addWidget(diameter_spin)
        layout.addLayout(diameter_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")
        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)
        
        dialog.setLayout(layout)
        
        # 连接信号
        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)
        
        if dialog.exec_() == QDialog.Accepted:
            equipment = equipment_combo.currentText()
            diameter = diameter_spin.value()
            
            # 更新所有炮孔
            updated_count = 0
            for hole in holes:
                hole.drilling_equipment = equipment
                hole.equipment_diameter = diameter
                hole.diameter_value = diameter  # 同时更新孔径值
                hole.update_diameter(diameter)  # 更新视觉大小
                hole.update_text_label()  # 更新标签显示
                hole.update_rock_display()  # 确保岩石标签也得到更新
                                  # 同时更新炸药信息为当前配置
                hole.explosive_type = self.current_explosive_type
                hole.charge_structure = getattr(self, 'current_charge_structure', '连续装药')
                  # 更新炸药相关标签显示
                if hasattr(hole, 'update_explosive_display'):
                    hole.update_explosive_display()
                updated_count += 1
            
            self.design_area.update_table_data()
            self.design_area.save_state()

            self.update_status(f"已将所有 {updated_count} 个炮孔设置为{equipment}(孔径{diameter}mm)")
    
    def update_grid_visibility(self):
        """更新网格可见性"""
        try:
            show_grid = self.view_config.get('grid', True)
            
            # 查找并设置网格线的可见性
            for item in self.design_area.graphics_scene.items():
                # 检查是否是网格线（网格线的特征：虚线样式、浅灰色、Z值为-1）
                if hasattr(item, 'pen'):
                    pen = item.pen()
                    if (hasattr(pen, 'style') and pen.style() == Qt.DotLine and 
                        hasattr(pen, 'color') and pen.color() == Qt.lightGray and
                        hasattr(item, 'zValue') and item.zValue() == -1):
                        item.setVisible(show_grid)
            
            print(f"网格显示状态更新为: {'显示' if show_grid else '隐藏'}")
            
        except Exception as e:
            print(f"更新网格显示状态时发生错误: {e}")
