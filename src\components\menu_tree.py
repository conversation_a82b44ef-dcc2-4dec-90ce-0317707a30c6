# -*- coding: utf-8 -*-
"""
左侧菜单树组件
实现树形结构的功能菜单
"""

from PyQt5.QtWidgets import QTreeWidget, QTreeWidgetItem, QHeaderView
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon, QFont

class MenuTreeWidget(QTreeWidget):
    """菜单树组件"""
    
    item_selected = pyqtSignal(str)  # 菜单项选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
        self.setup_menu_items()
    
    def init_ui(self):
        """初始化界面"""
        # 设置标题
        self.setHeaderLabel("功能菜单")
        
        # 设置样式
        self.setStyleSheet("""
            QTreeWidget {
                background-color: #fafafa;
                border: 1px solid #d0d0d0;
                font-size: 9pt;
                outline: none;
            }
            QTreeWidget::item {
                padding: 4px;
                border: none;
            }
            QTreeWidget::item:hover {
                background-color: #e8f4fd;
            }
            QTreeWidget::item:selected {
                background-color: #316AC5;
                color: white;
            }
            QTreeWidget::branch {
                background-color: transparent;
            }
            QTreeWidget::branch:has-children:closed {
                image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFHSURBVBiVY/j//z8DAwMD448fPxj+/v3L8OfPH4Y/f/4w/P79m+HPnz8Mf/78Yfj9+zfD79+/GX7//s3w+/dvht+/fzP8/v2b4ffv3wy/f/9m+P37N8Pv378Zfv/+zfD792+G379/M/z+/Zvh9+/fDL9//2b4/fs3w+/fvxl+//7N8Pv3b4bfv38z/P79m+H3798Mv3//Zvj9+zfD79+/GX7//s3w+/dvht+/fzP8/v2b4ffv3wy/f/9m+P37N8Pv378Zfv/+zfD792+G379/M/z+/Zvh9+/fDL9//2b4/fs3w+/fvxl+//7N8Pv3b4bfv38z/P79m+H3798Mv3//Zvj9+zfD79+/GX7//s3w+/dvht+/fzP8/v2b4ffv3wy/f/9m+P37N8Pv378Zfv/+zfD792+G379/M/z+/Zvh9+/fDL9//2b4/fs3w+/fvxkYGBgYAJZoqM8pTt8wAAAAAElFTkSuQmCC);
            }
            QTreeWidget::branch:has-children:open {
                image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAJCAYAAADgkQYQAAAABHNCSVQICAgIfAhkiAAAAAlwSFlzAAAAdgAAAHYBTnsmCAAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAFJSURBVBiVY/j//z8DAwMD448fPxj+/v3L8OfPH4Y/f/4w/P79m+HPnz8Mf/78Yfj9+zfD79+/GX7//s3w+/dvht+/fzP8/v2b4ffv3wy/f/9m+P37N8Pv378Zfv/+zfD792+G379/M/z+/Zvh9+/fDL9//2b4/fs3w+/fvxl+//7N8Pv3b4bfv38z/P79m+H3798Mv3//Zvj9+zfD79+/GX7//s3w+/dvht+/fzP8/v2b4ffv3wy/f/9m+P37N8Pv378Zfv/+zfD792+G379/M/z+/Zvh9+/fDL9//2b4/fs3w+/fvxl+//7N8Pv3b4bfv38z/P79m+H3798Mv3//ZmBgYGAAWaSqz+kQM8wAAAAASUVORK5CYII=);
            }
        """)
        
        # 设置基本属性
        self.setRootIsDecorated(True)
        self.setAlternatingRowColors(False)
        self.setExpandsOnDoubleClick(True)
        
        # 设置选择模式
        self.setSelectionMode(QTreeWidget.SingleSelection)
    
    def setup_menu_items(self):
        """设置菜单项"""
        # 清空现有项目
        self.clear()
        
        # 设置组标题字体
        font = QFont()
        font.setBold(True)
        
        # 编辑菜单组
        edit_group = QTreeWidgetItem(self, ["编辑"])
        edit_group.setExpanded(True)  # 常用功能，默认展开
        edit_group.setFont(0, font)
        
        self.undo_item = QTreeWidgetItem(edit_group, ["撤销"])
        self.redo_item = QTreeWidgetItem(edit_group, ["重做"])
        
        # 文件菜单组
        file_group = QTreeWidgetItem(self, ["文件"])
        file_group.setExpanded(True)  # 常用功能，默认展开
        file_group.setFont(0, font)
        
        QTreeWidgetItem(file_group, ["新建"])
        QTreeWidgetItem(file_group, ["打开"])
        QTreeWidgetItem(file_group, ["保存"])
        QTreeWidgetItem(file_group, ["另存为"])
        
        QTreeWidgetItem(file_group, ["数据导入"])
        QTreeWidgetItem(file_group, ["图形打印"])
        
        # 参数菜单组
        param_group = QTreeWidgetItem(self, ["参数"])
        param_group.setExpanded(False)  # 参数设置不常用，默认折叠
        param_group.setFont(0, font)
        
        QTreeWidgetItem(param_group, ["岩石参数"])
        QTreeWidgetItem(param_group, ["炸药参数"])
        QTreeWidgetItem(param_group, ["打孔设备"])
        
        # 设计菜单组
        design_group = QTreeWidgetItem(self, ["设计"])
        design_group.setExpanded(True)  # 核心功能，默认展开
        design_group.setFont(0, font)
        
        # 初步设计子组
        preliminary_group = QTreeWidgetItem(design_group, ["初步设计"])
        preliminary_group.setExpanded(True)  # 常用设计功能，默认展开
        
        # 炮孔初步设计子组
        hole_design_group = QTreeWidgetItem(preliminary_group, ["炮孔初步设计"])
        hole_design_group.setExpanded(False)  # 炮孔功能较多，默认折叠
        QTreeWidgetItem(hole_design_group, ["增加炮孔"])
        QTreeWidgetItem(hole_design_group, ["通过坐标添加"])
        QTreeWidgetItem(hole_design_group, ["删除炮孔"])
        QTreeWidgetItem(hole_design_group, ["清空所有炮孔"])
        QTreeWidgetItem(hole_design_group, ["移动炮孔"])
        QTreeWidgetItem(hole_design_group, ["修改数据"])
        QTreeWidgetItem(hole_design_group, ["智能布孔"])
        QTreeWidgetItem(hole_design_group, ["退出操作模式"])
        
        QTreeWidgetItem(preliminary_group, ["孔类型设置"])
        
        # 精细化设计子组
        detailed_group = QTreeWidgetItem(design_group, ["精细化设计"])
        detailed_group.setExpanded(False)  # 详细功能较多，默认折叠
        QTreeWidgetItem(detailed_group, ["孔径"])
        QTreeWidgetItem(detailed_group, ["岩石"])
        QTreeWidgetItem(detailed_group, ["炸药"])
        QTreeWidgetItem(detailed_group, ["间排距"])
        QTreeWidgetItem(detailed_group, ["连接成排"])
        QTreeWidgetItem(detailed_group, ["编辑炸药量"])
        QTreeWidgetItem(detailed_group, ["作业面"])
        QTreeWidgetItem(detailed_group, ["边界线"])
        QTreeWidgetItem(detailed_group, ["文本"])
        
        # 输出菜单组
        output_group = QTreeWidgetItem(self, ["输出"])
        output_group.setExpanded(False)  # 输出功能不常用，默认折叠
        output_group.setFont(0, font)
        
        QTreeWidgetItem(output_group, ["爆破网络图"])
        QTreeWidgetItem(output_group, ["爆破参数表"])
        
        # 本地文件菜单组
        local_group = QTreeWidgetItem(self, ["本地文件"])
        local_group.setExpanded(False)  # 文件管理不常用，默认折叠
        local_group.setFont(0, font)
        
        QTreeWidgetItem(local_group, ["文件管理器"])
    
    def mousePressEvent(self, event):
        """鼠标点击事件"""
        item = self.itemAt(event.pos())
        
        if item:
            if item.parent():  # 子项目 - 发射选择信号
                self.item_selected.emit(item.text(0))
                super().mousePressEvent(event)
            else:  # 一级菜单项 - 处理展开/折叠
                # 切换展开/折叠状态
                if item.isExpanded():
                    item.setExpanded(False)
                else:
                    item.setExpanded(True)
                # 不调用super()，避免默认的选择行为
        else:
            super().mousePressEvent(event)
    
    def get_selected_item_text(self):
        """获取选中项目的文本"""
        current_item = self.currentItem()
        if current_item:
            return current_item.text(0)
        return ""
    
    def update_undo_redo_status(self, can_undo, can_redo):
        """更新撤销/重做菜单项的状态"""
        if hasattr(self, 'undo_item'):
            self.undo_item.setDisabled(not can_undo)
        if hasattr(self, 'redo_item'):
            self.redo_item.setDisabled(not can_redo) 