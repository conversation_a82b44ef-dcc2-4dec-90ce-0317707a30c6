# -*- coding: utf-8 -*-
"""
布孔参数调整对话框
用户可以确认和调整计算结果
"""

import sys
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                             QLabel, QDoubleSpinBox, QSpinBox, QPushButton, 
                             QGroupBox, QMessageBox, QTextEdit, QTabWidget, 
                             QWidget, QFrame, QGridLayout)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class LayoutAdjustmentDialog(QDialog):
    """布孔参数调整对话框"""
    
    def __init__(self, layout_type, calculation_results, parent=None):
        super().__init__(parent)
        self.layout_type = layout_type
        self.calculation_results = calculation_results
        self.adjusted_params = {}
        
        self.setWindowTitle(f"{layout_type}布孔参数调整")
        self.setModal(True)
        self.resize(600, 500)
        
        self.init_ui()
        self.load_calculated_values()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(f"{self.layout_type}布孔参数调整")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("请确认或调整以下计算结果，然后选择布孔方式：")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)
        
        # 创建选项卡
        self.tab_widget = QTabWidget()
        
        # 参数调整选项卡
        self.adjustment_tab = QWidget()
        self.init_adjustment_tab()
        self.tab_widget.addTab(self.adjustment_tab, "参数调整")
        
        # 布孔设置选项卡
        self.layout_tab = QWidget()
        self.init_layout_tab()
        self.tab_widget.addTab(self.layout_tab, "布孔设置")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.confirm_btn = QPushButton("确认布孔")
        self.confirm_btn.clicked.connect(self.accept)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.confirm_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def init_adjustment_tab(self):
        """初始化参数调整选项卡"""
        layout = QVBoxLayout()
        
        # 计算结果组
        result_group = QGroupBox("计算结果（可调整）")
        result_layout = QFormLayout()
        
        # 孔距
        self.hole_spacing_spinbox = QDoubleSpinBox()
        self.hole_spacing_spinbox.setRange(1.0, 50.0)
        self.hole_spacing_spinbox.setSuffix(" m")
        self.hole_spacing_spinbox.setDecimals(2)
        result_layout.addRow("孔距:", self.hole_spacing_spinbox)
        
        # 排距
        self.row_spacing_spinbox = QDoubleSpinBox()
        self.row_spacing_spinbox.setRange(1.0, 50.0)
        self.row_spacing_spinbox.setSuffix(" m")
        self.row_spacing_spinbox.setDecimals(2)
        result_layout.addRow("排距:", self.row_spacing_spinbox)
        
        # 炸药量
        self.explosive_amount_spinbox = QDoubleSpinBox()
        self.explosive_amount_spinbox.setRange(0.1, 1000.0)
        self.explosive_amount_spinbox.setSuffix(" kg")
        self.explosive_amount_spinbox.setDecimals(2)
        result_layout.addRow("炸药量:", self.explosive_amount_spinbox)
        
        # 单位长度装药量
        self.unit_charge_label = QLabel()
        result_layout.addRow("单位长度装药量:", self.unit_charge_label)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        # 限制条件组
        constraint_group = QGroupBox("限制条件")
        constraint_layout = QFormLayout()
        
        # 最大装药能力
        self.max_charge_label = QLabel()
        constraint_layout.addRow("最大装药能力:", self.max_charge_label)
        
        # 最小填塞长度
        self.min_stemming_label = QLabel()
        constraint_layout.addRow("最小填塞长度:", self.min_stemming_label)
        
        constraint_group.setLayout(constraint_layout)
        layout.addWidget(constraint_group)
        
        # 连接信号
        self.explosive_amount_spinbox.valueChanged.connect(self.update_unit_charge)
        
        layout.addStretch()
        self.adjustment_tab.setLayout(layout)
    
    def init_layout_tab(self):
        """初始化布孔设置选项卡"""
        layout = QVBoxLayout()
        
        if self.layout_type == "一排":
            # 一排布孔设置
            layout_group = QGroupBox("一排布孔设置")
            grid_layout = QGridLayout()

            # 起点坐标
            grid_layout.addWidget(QLabel("起点 X:"), 0, 0)
            self.start_x_spin = QDoubleSpinBox()
            self.start_x_spin.setRange(-9999, 9999)
            grid_layout.addWidget(self.start_x_spin, 0, 1)

            grid_layout.addWidget(QLabel("起点 Y:"), 0, 2)
            self.start_y_spin = QDoubleSpinBox()
            self.start_y_spin.setRange(-9999, 9999)
            grid_layout.addWidget(self.start_y_spin, 0, 3)

            # 终点坐标
            grid_layout.addWidget(QLabel("终点 X:"), 1, 0)
            self.end_x_spin = QDoubleSpinBox()
            self.end_x_spin.setRange(-9999, 9999)
            grid_layout.addWidget(self.end_x_spin, 1, 1)
            
            grid_layout.addWidget(QLabel("终点 Y:"), 1, 2)
            self.end_y_spin = QDoubleSpinBox()
            self.end_y_spin.setRange(-9999, 9999)
            grid_layout.addWidget(self.end_y_spin, 1, 3)

            layout_group.setLayout(grid_layout)
            layout.addWidget(layout_group)
            
        else:
            # 爆区布孔设置
            layout_group = QGroupBox("爆区布孔设置")
            grid_layout = QGridLayout()

            # 排数和每排孔数
            grid_layout.addWidget(QLabel("排数:"), 0, 0)
            self.row_count_spinbox = QSpinBox()
            self.row_count_spinbox.setRange(1, 50)
            self.row_count_spinbox.setValue(3)
            grid_layout.addWidget(self.row_count_spinbox, 0, 1)

            grid_layout.addWidget(QLabel("每排孔数:"), 0, 2)
            self.holes_per_row_spinbox = QSpinBox()
            self.holes_per_row_spinbox.setRange(1, 100)
            self.holes_per_row_spinbox.setValue(5)
            grid_layout.addWidget(self.holes_per_row_spinbox, 0, 3)

            # 四个角点坐标
            for i in range(4):
                grid_layout.addWidget(QLabel(f"角点{i+1} X:"), i + 1, 0)
                setattr(self, f'p{i+1}_x_spin', QDoubleSpinBox())
                spin_x = getattr(self, f'p{i+1}_x_spin')
                spin_x.setRange(-9999, 9999)
                grid_layout.addWidget(spin_x, i + 1, 1)

                grid_layout.addWidget(QLabel(f"角点{i+1} Y:"), i + 1, 2)
                setattr(self, f'p{i+1}_y_spin', QDoubleSpinBox())
                spin_y = getattr(self, f'p{i+1}_y_spin')
                spin_y.setRange(-9999, 9999)
                grid_layout.addWidget(spin_y, i + 1, 3)
            
            layout_group.setLayout(grid_layout)
            layout.addWidget(layout_group)
        
        # 添加一个说明，告知用户可以留空坐标以在地图上选择
        info_label = QLabel("提示：坐标值可留空或保持为0，确认后将在设计图上通过鼠标点击来指定位置。")
        info_label.setWordWrap(True)
        info_label.setStyleSheet("color: gray; font-style: italic;")
        layout.addWidget(info_label)
        
        layout.addStretch()
        self.layout_tab.setLayout(layout)
    
    def load_calculated_values(self):
        """加载计算值"""
        if not self.calculation_results:
            return
        
        # 设置计算结果
        self.hole_spacing_spinbox.setValue(self.calculation_results.get('hole_spacing', 3.0))
        self.row_spacing_spinbox.setValue(self.calculation_results.get('row_spacing', 3.0))
        self.explosive_amount_spinbox.setValue(self.calculation_results.get('explosive_amount', 10.0))
        
        # 设置限制条件标签
        max_charge = self.calculation_results.get('max_charge_capacity', 0)
        self.max_charge_label.setText(f"{max_charge:.2f} kg")
        
        min_stemming = self.calculation_results.get('min_stemming', 0)
        self.min_stemming_label.setText(f"{min_stemming:.2f} m")
        
        # 更新单位长度装药量
        self.update_unit_charge()
    
    def update_unit_charge(self):
        """更新单位长度装药量"""
        try:
            explosive_amount = self.explosive_amount_spinbox.value()
            charge_length = self.calculation_results.get('charge_length', 1.0)
            
            if charge_length > 0:
                unit_charge = explosive_amount / charge_length
                self.unit_charge_label.setText(f"{unit_charge:.2f} kg/m")
            else:
                self.unit_charge_label.setText("-- kg/m")
                
        except Exception as e:
            self.unit_charge_label.setText("-- kg/m")
    
    def validate_parameters(self):
        """验证参数"""
        # 检查炸药量是否超过最大装药能力
        explosive_amount = self.explosive_amount_spinbox.value()
        max_charge = self.calculation_results.get('max_charge_capacity', 0)
        
        if explosive_amount > max_charge:
            QMessageBox.warning(self, "参数错误", 
                              f"炸药量({explosive_amount:.2f}kg)超过理论最大装药量({max_charge:.2f}kg)")
            return False
        
        return True
    
    def get_adjusted_parameters(self):
        """获取调整后的参数"""
        params = {
            'hole_spacing': self.hole_spacing_spinbox.value(),
            'row_spacing': self.row_spacing_spinbox.value(),
            'explosive_amount': self.explosive_amount_spinbox.value(),
            'unit_charge': float(self.unit_charge_label.text().replace(' kg/m', '')) if 'kg/m' in self.unit_charge_label.text() else 0.0
        }
        
        if self.layout_type == "爆区":
            params.update({
                'row_count': self.row_count_spinbox.value(),
                'holes_per_row': self.holes_per_row_spinbox.value(),
                'points': [
                    (self.p1_x_spin.value(), self.p1_y_spin.value()),
                    (self.p2_x_spin.value(), self.p2_y_spin.value()),
                    (self.p3_x_spin.value(), self.p3_y_spin.value()),
                    (self.p4_x_spin.value(), self.p4_y_spin.value()),
                ]
            })
        else: # 一排
            params.update({
                'points': [
                    (self.start_x_spin.value(), self.start_y_spin.value()),
                    (self.end_x_spin.value(), self.end_y_spin.value()),
                ]
            })
        
        return params
    
    def accept(self):
        """确认对话框"""
        if not self.validate_parameters():
            return
        
        # 保存调整后的参数
        self.adjusted_params = self.get_adjusted_parameters()
        
        super().accept()

if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 测试数据
    test_results = {
        'hole_spacing': 3.5,
        'row_spacing': 4.0,
        'explosive_amount': 15.5,
        'max_charge_capacity': 25.0,
        'min_stemming': 2.5,
        'charge_length': 8.0
    }
    
    dialog = LayoutAdjustmentDialog("爆区", test_results)
    dialog.show()
    sys.exit(app.exec_()) 