# -*- coding: utf-8 -*-
"""
文件操作对话框
包含新建、导入、打印等功能的对话框
"""

import json
import csv
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QComboBox, QPushButton, QTableWidget,
                            QTableWidgetItem, QHeaderView, QGroupBox, QSpinBox,
                            QDoubleSpinBox, QTextEdit, QTabWidget, QWidget,
                            QMessageBox, QLabel, QFileDialog, QCheckBox,
                            QListWidget, QListWidgetItem, QProgressBar,
                            QScrollArea, QFrame, QGridLayout, QRadioButton,
                            QButtonGroup)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QRectF
from PyQt5.QtGui import QFont, QPixmap, Q<PERSON><PERSON>ter, QPen

class NewDesignDialog(QDialog):
    """新建设计对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("新建设计")
        self.setModal(True)
        self.resize(400, 200)
        self.design_name = ""
        self.blast_level = 0.0
        self.step_height = 0.0
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 设计名称输入
        form_layout = QFormLayout()
        
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("请输入设计名称")
        form_layout.addRow("设计名称:", self.name_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.ok_button.clicked.connect(self.on_ok_clicked)
        self.cancel_button.clicked.connect(self.reject)
    
    def on_ok_clicked(self):
        """确定按钮点击事件"""
        design_name = self.name_edit.text().strip()
        if not design_name:
            QMessageBox.warning(self, "输入错误", "请输入设计名称！")
            return
        
        self.design_name = design_name
        
        # 弹出第二个对话框设置爆破参数
        param_dialog = BlastParameterDialog(self)
        if param_dialog.exec_() == QDialog.Accepted:
            self.blast_level = param_dialog.blast_level
            self.step_height = param_dialog.step_height
            self.accept()

class BlastParameterDialog(QDialog):
    """爆破参数设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("爆破参数设置")
        self.setModal(True)
        self.resize(350, 180)
        self.blast_level = 0.0
        self.step_height = 0.0
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 参数输入
        form_layout = QFormLayout()
        
        self.blast_level_edit = QDoubleSpinBox()
        self.blast_level_edit.setRange(-1000, 1000)
        self.blast_level_edit.setDecimals(2)
        self.blast_level_edit.setSuffix(" m")
        self.blast_level_edit.setValue(0.0)
        form_layout.addRow("爆破水平:", self.blast_level_edit)
        
        self.step_height_edit = QDoubleSpinBox()
        self.step_height_edit.setRange(1, 100)
        self.step_height_edit.setDecimals(2)
        self.step_height_edit.setSuffix(" m")
        self.step_height_edit.setValue(15.0)
        form_layout.addRow("台阶段高:", self.step_height_edit)
        
        layout.addLayout(form_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.complete_button = QPushButton("完成")
        self.cancel_button = QPushButton("取消")
        
        button_layout.addStretch()
        button_layout.addWidget(self.complete_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.complete_button.clicked.connect(self.on_complete_clicked)
        self.cancel_button.clicked.connect(self.reject)
    
    def on_complete_clicked(self):
        """完成按钮点击事件"""
        self.blast_level = self.blast_level_edit.value()
        self.step_height = self.step_height_edit.value()
        self.accept()

class DataImportDialog(QDialog):
    """数据导入对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("数据导入")
        self.setModal(True)
        self.resize(800, 600)
        self.import_type = ""
        self.file_path = ""
        self.column_mapping = {}
        self.imported_data = []
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 导入类型选择选项卡
        self.setup_import_type_tab(tab_widget)
        
        # 文件选择选项卡
        self.setup_file_selection_tab(tab_widget)
        
        # 数据预览和列映射选项卡
        self.setup_data_mapping_tab(tab_widget)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.prev_button = QPushButton("上一步")
        self.next_button = QPushButton("下一步")
        self.import_button = QPushButton("导入")
        self.cancel_button = QPushButton("取消")
        
        self.prev_button.setEnabled(False)
        self.import_button.setEnabled(False)
        
        button_layout.addWidget(self.prev_button)
        button_layout.addStretch()
        button_layout.addWidget(self.next_button)
        button_layout.addWidget(self.import_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.prev_button.clicked.connect(self.prev_step)
        self.next_button.clicked.connect(self.next_step)
        self.import_button.clicked.connect(self.import_data)
        self.cancel_button.clicked.connect(self.reject)
    
    def setup_import_type_tab(self, tab_widget):
        """设置导入类型选择选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 说明标签
        info_label = QLabel("请选择要导入的数据类型：")
        info_label.setFont(QFont("Microsoft YaHei", 10))
        layout.addWidget(info_label)
        
        # 导入类型选择
        self.import_type_group = QButtonGroup()
        
        types = [
            ("hole", "炮孔数据", "导入炮孔的孔号、坐标(X,Y,Z)和孔深等信息"),
            ("work_surface", "作业面数据", "导入作业面的坐标点信息"),
            ("boundary", "边界线数据", "导入爆区边界线的坐标点信息"),
            ("text", "文本标注数据", "导入文本标注的位置和内容信息")
        ]
        
        for i, (value, title, desc) in enumerate(types):
            radio = QRadioButton(title)
            radio.setProperty("import_type", value)
            self.import_type_group.addButton(radio, i)
            
            # 添加描述
            desc_label = QLabel(f"    {desc}")
            desc_label.setStyleSheet("color: #666; margin-bottom: 10px;")
            
            layout.addWidget(radio)
            layout.addWidget(desc_label)
        
        # 默认选择第一个
        self.import_type_group.button(0).setChecked(True)
        
        layout.addStretch()
        tab_widget.addTab(widget, "1. 选择数据类型")
    
    def setup_file_selection_tab(self, tab_widget):
        """设置文件选择选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 文件选择区域
        file_group = QGroupBox("选择数据文件")
        file_layout = QVBoxLayout(file_group)
        
        # 文件路径显示
        path_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setPlaceholderText("请选择要导入的数据文件...")
        
        self.browse_button = QPushButton("浏览...")
        self.browse_button.clicked.connect(self.browse_file)
        
        path_layout.addWidget(QLabel("文件路径:"))
        path_layout.addWidget(self.file_path_edit)
        path_layout.addWidget(self.browse_button)
        
        file_layout.addLayout(path_layout)
        
        # 支持的文件格式说明
        format_label = QLabel("支持的文件格式：CSV (*.csv), JSON (*.json), 文本文件 (*.txt)")
        format_label.setStyleSheet("color: #666; font-size: 9pt;")
        file_layout.addWidget(format_label)
        
        layout.addWidget(file_group)
        layout.addStretch()
        
        tab_widget.addTab(widget, "2. 选择文件")
    
    def setup_data_mapping_tab(self, tab_widget):
        """设置数据预览和列映射选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据预览区域
        preview_group = QGroupBox("数据预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_table = QTableWidget()
        self.preview_table.setMaximumHeight(200)
        preview_layout.addWidget(self.preview_table)
        
        layout.addWidget(preview_group)
        
        # 列映射区域
        mapping_group = QGroupBox("列映射设置")
        mapping_layout = QFormLayout(mapping_group)
        
        # 系统字段映射
        self.mapping_combos = {}
        system_fields = {
            "hole_id": "孔号",
            "x_coord": "X坐标",
            "y_coord": "Y坐标", 
            "z_coord": "Z坐标",
            "depth": "孔深",
            "text": "文本内容"
        }
        
        for field_key, field_name in system_fields.items():
            combo = QComboBox()
            combo.addItem("-- 请选择列 --")
            self.mapping_combos[field_key] = combo
            mapping_layout.addRow(f"{field_name}:", combo)
        
        layout.addWidget(mapping_group)
        
        tab_widget.addTab(widget, "3. 数据映射")
    
    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择数据文件", "", 
            "数据文件 (*.csv *.json *.txt);;CSV文件 (*.csv);;JSON文件 (*.json);;文本文件 (*.txt)")
        
        if file_path:
            self.file_path = file_path
            self.file_path_edit.setText(file_path)
            self.load_file_preview()
    
    def load_file_preview(self):
        """加载文件预览"""
        if not self.file_path:
            return
        
        try:
            # 根据文件扩展名选择解析方式
            ext = os.path.splitext(self.file_path)[1].lower()
            
            if ext == '.csv':
                self.load_csv_preview()
            elif ext == '.json':
                self.load_json_preview()
            elif ext == '.txt':
                self.load_txt_preview()
                
        except Exception as e:
            QMessageBox.critical(self, "文件读取错误", f"无法读取文件：{str(e)}")
    
    def load_csv_preview(self):
        """加载CSV文件预览"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            rows = list(reader)
            
        if not rows:
            QMessageBox.warning(self, "文件错误", "文件为空或格式不正确")
            return
        
        # 设置表格
        headers = rows[0]
        data_rows = rows[1:6]  # 只显示前5行作为预览
        
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setRowCount(len(data_rows))
        self.preview_table.setHorizontalHeaderLabels(headers)
        
        for row, row_data in enumerate(data_rows):
            for col, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                self.preview_table.setItem(row, col, item)
        
        # 更新列映射下拉框
        self.update_mapping_combos(headers)
    
    def load_json_preview(self):
        """加载JSON文件预览"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list) or not data:
            QMessageBox.warning(self, "文件错误", "JSON文件格式不正确，应为对象数组")
            return
        
        # 获取字段名
        headers = list(data[0].keys())
        data_rows = data[:5]  # 只显示前5行
        
        self.preview_table.setColumnCount(len(headers))
        self.preview_table.setRowCount(len(data_rows))
        self.preview_table.setHorizontalHeaderLabels(headers)
        
        for row, row_data in enumerate(data_rows):
            for col, header in enumerate(headers):
                value = row_data.get(header, "")
                item = QTableWidgetItem(str(value))
                self.preview_table.setItem(row, col, item)
        
        # 更新列映射下拉框
        self.update_mapping_combos(headers)
    
    def load_txt_preview(self):
        """加载TXT文件预览"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            QMessageBox.warning(self, "文件错误", "文件为空")
            return
        
        # 假设是制表符或空格分隔的数据
        sample_lines = lines[:6]
        max_cols = 0
        data_rows = []
        
        for line in sample_lines:
            # 尝试不同的分隔符
            if '\t' in line:
                parts = line.strip().split('\t')
            else:
                parts = line.strip().split()
            
            data_rows.append(parts)
            max_cols = max(max_cols, len(parts))
        
        # 生成默认列名
        headers = [f"列{i+1}" for i in range(max_cols)]
        
        self.preview_table.setColumnCount(max_cols)
        self.preview_table.setRowCount(len(data_rows))
        self.preview_table.setHorizontalHeaderLabels(headers)
        
        for row, row_data in enumerate(data_rows):
            for col in range(max_cols):
                value = row_data[col] if col < len(row_data) else ""
                item = QTableWidgetItem(str(value))
                self.preview_table.setItem(row, col, item)
        
        # 更新列映射下拉框
        self.update_mapping_combos(headers)
    
    def update_mapping_combos(self, headers):
        """更新列映射下拉框"""
        for combo in self.mapping_combos.values():
            combo.clear()
            combo.addItem("-- 请选择列 --")
            combo.addItems(headers)
    
    def prev_step(self):
        """上一步"""
        current_index = self.findChild(QTabWidget).currentIndex()
        if current_index > 0:
            self.findChild(QTabWidget).setCurrentIndex(current_index - 1)
            self.update_buttons()
    
    def next_step(self):
        """下一步"""
        current_index = self.findChild(QTabWidget).currentIndex()
        tab_widget = self.findChild(QTabWidget)
        
        if current_index == 0:  # 从类型选择到文件选择
            selected_button = self.import_type_group.checkedButton()
            if selected_button:
                self.import_type = selected_button.property("import_type")
                tab_widget.setCurrentIndex(1)
        elif current_index == 1:  # 从文件选择到数据映射
            if not self.file_path:
                QMessageBox.warning(self, "提示", "请先选择数据文件")
                return
            tab_widget.setCurrentIndex(2)
        
        self.update_buttons()
    
    def update_buttons(self):
        """更新按钮状态"""
        current_index = self.findChild(QTabWidget).currentIndex()
        
        self.prev_button.setEnabled(current_index > 0)
        self.next_button.setEnabled(current_index < 2)
        self.import_button.setEnabled(current_index == 2 and bool(self.file_path))
    
    def import_data(self):
        """导入数据"""
        # 检查列映射
        mapping = {}
        for field_key, combo in self.mapping_combos.items():
            if combo.currentIndex() > 0:  # 不是"请选择列"
                mapping[field_key] = combo.currentText()
        
        # 根据导入类型验证必需的列映射
        required_fields = self.get_required_fields()
        missing_fields = [
            self.get_field_display_name(field) 
            for field in required_fields if field not in mapping
        ]
        
        if missing_fields:
            QMessageBox.warning(self, "映射错误", 
                               f"以下必需字段未设置列映射：\n{', '.join(missing_fields)}")
            return
        
        self.column_mapping = mapping
        
        try:
            # 解析原始数据
            raw_data = self.parse_raw_data()
            
            # 根据映射关系转换数据
            self.imported_data = self.transform_data(raw_data, mapping)
            
            # 数据校验
            valid_data, invalid_count, error_messages = self.validate_import_data()
            
            if invalid_count > 0:
                message = f"数据校验完成：\n" \
                          f"有效数据：{len(valid_data)} 条\n" \
                          f"无效数据：{invalid_count} 条\n\n"
                if error_messages:
                    message += "错误详情：\n" + "\n".join(error_messages[:5])
                    if len(error_messages) > 5:
                        message += f"\n...还有 {len(error_messages)-5} 个错误"
                
                reply = QMessageBox.question(self, "数据校验", 
                                           message + "\n\n是否继续导入有效数据？",
                                           QMessageBox.Yes | QMessageBox.No)
                if reply != QMessageBox.Yes:
                    return
            
            self.imported_data = valid_data
            
            if not self.imported_data:
                QMessageBox.warning(self, "导入失败", "没有有效数据可以导入")
                return
            
            QMessageBox.information(self, "导入成功", 
                                  f"成功导入 {len(self.imported_data)} 条数据")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "导入失败", f"数据导入失败：{str(e)}")

    def parse_raw_data(self):
        """解析原始文件数据，返回字典列表"""
        ext = os.path.splitext(self.file_path)[1].lower()
        if ext == '.csv':
            with open(self.file_path, 'r', encoding='utf-8-sig') as f:
                return list(csv.DictReader(f))
        elif ext == '.json':
            with open(self.file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        elif ext == '.txt':
            # TXT文件处理较为复杂，这里简化为假设第一行为表头，制表符分隔
            with open(self.file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            if not lines: return []
            headers = lines[0].strip().split('\t')
            return [dict(zip(headers, line.strip().split('\t'))) for line in lines[1:]]
        return []

    def transform_data(self, raw_data, mapping):
        """根据映射关系，将原始数据转换为内部格式"""
        transformed_data = []
        for raw_row in raw_data:
            new_row = {}
            for field_key, column_name in mapping.items():
                new_row[field_key] = raw_row.get(column_name, "")
            transformed_data.append(new_row)
        return transformed_data
    
    def get_required_fields(self):
        """获取当前导入类型的必需字段"""
        if self.import_type == "hole":
            return ["hole_id", "x_coord", "y_coord"]  # 孔号和XY坐标是必需的
        elif self.import_type in ["work_surface", "boundary"]:
            return ["x_coord", "y_coord"]  # 坐标是必需的
        elif self.import_type == "text":
            return ["x_coord", "y_coord", "text"]  # 坐标和文本内容是必需的
        return []
    
    def get_field_display_name(self, field_key):
        """获取字段的显示名称"""
        field_names = {
            "hole_id": "孔号",
            "x_coord": "X坐标",
            "y_coord": "Y坐标",
            "z_coord": "Z坐标",
            "depth": "孔深",
            "text": "文本内容"
        }
        return field_names.get(field_key, field_key)
    
    def validate_import_data(self):
        """验证导入数据"""
        valid_data = []
        error_messages = []
        
        for i, row in enumerate(self.imported_data):
            try:
                # 基础数据类型验证
                if self.import_type == "hole":
                    # 验证坐标和孔深
                    x = float(row.get('x_coord', 0))
                    y = float(row.get('y_coord', 0))
                    
                    z_str = row.get('z_coord')
                    z = float(z_str) if z_str and z_str.strip() else None

                    depth_str = row.get('depth')
                    depth = float(depth_str) if depth_str and depth_str.strip() else 15.0

                    hole_id = str(row.get('hole_id', '')).strip()
                    
                    if not hole_id:
                        error_messages.append(f"第{i+1}行：孔号不能为空")
                        continue
                    
                    if depth <= 0:
                        error_messages.append(f"第{i+1}行：孔深必须大于0")
                        continue
                    
                    # 更新行数据
                    row['x_coord'] = x
                    row['y_coord'] = y
                    if z is not None:
                        row['z_coord'] = z
                    row['depth'] = depth
                    row['hole_id'] = hole_id
                    
                elif self.import_type in ["work_surface", "boundary"]:
                    # 验证坐标
                    x = float(row.get('x_coord', 0))
                    y = float(row.get('y_coord', 0))
                    row['x_coord'] = x
                    row['y_coord'] = y
                    
                elif self.import_type == "text":
                    # 验证坐标和文本
                    x = float(row.get('x_coord', 0))
                    y = float(row.get('y_coord', 0))
                    text = str(row.get('text', '')).strip()
                    
                    if not text:
                        error_messages.append(f"第{i+1}行：文本内容不能为空")
                        continue
                    
                    row['x_coord'] = x
                    row['y_coord'] = y
                    row['text'] = text
                
                valid_data.append(row)
                
            except ValueError as e:
                error_messages.append(f"第{i+1}行：数据格式错误 - {str(e)}")
            except Exception as e:
                error_messages.append(f"第{i+1}行：验证失败 - {str(e)}")
        
        return valid_data, len(self.imported_data) - len(valid_data), error_messages

class PrintSettingsDialog(QDialog):
    """打印设置对话框"""
    
    def __init__(self, design_area, parent=None):
        super().__init__(parent)
        self.setWindowTitle("图形打印设置")
        self.setModal(True)
        self.resize(600, 500)
        self.design_area = design_area
        self.print_scale = "1:100"
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 预览区域
        preview_group = QGroupBox("打印预览")
        preview_layout = QVBoxLayout(preview_group)
        
        # 创建滚动区域包装预览标签
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumSize(450, 400)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 创建预览标签
        self.preview_label = QLabel()
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background: white;")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumSize(200, 200)
        
        scroll_area.setWidget(self.preview_label)
        preview_layout.addWidget(scroll_area)
        layout.addWidget(preview_group)
        
        # 设置区域
        settings_group = QGroupBox("打印设置")
        settings_layout = QFormLayout(settings_group)
        
        # 打印比例选择
        self.scale_combo = QComboBox()
        self.scale_combo.addItems(["1:50", "1:100", "1:200", "1:500", "1:1000"])
        self.scale_combo.setCurrentText("1:100")
        self.scale_combo.currentTextChanged.connect(self.update_preview)
        settings_layout.addRow("打印比例:", self.scale_combo)
        
        # 纸张设置
        self.paper_combo = QComboBox()
        self.paper_combo.addItems(["A4", "A3", "A2", "A1", "A0"])
        self.paper_combo.currentTextChanged.connect(self.update_preview)
        settings_layout.addRow("纸张大小:", self.paper_combo)
        
        # 方向设置
        self.orientation_combo = QComboBox()
        self.orientation_combo.addItems(["纵向", "横向"])
        self.orientation_combo.currentTextChanged.connect(self.update_preview)
        settings_layout.addRow("打印方向:", self.orientation_combo)
        
        layout.addWidget(settings_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.preview_button = QPushButton("更新预览")
        self.print_button = QPushButton("打印")
        self.cancel_button = QPushButton("取消")
        
        button_layout.addWidget(self.preview_button)
        button_layout.addStretch()
        button_layout.addWidget(self.print_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.preview_button.clicked.connect(self.update_preview)
        self.print_button.clicked.connect(self.print_graphics)
        self.cancel_button.clicked.connect(self.reject)
        
        # 初始预览
        self.update_preview()
    
    def update_preview(self):
        """更新预览"""
        self.print_scale = self.scale_combo.currentText()
        paper_size = self.paper_combo.currentText()
        orientation = self.orientation_combo.currentText()
        
        # 根据纸张大小和方向确定预览尺寸
        paper_ratios = {
            "A4": (210, 297),
            "A3": (297, 420), 
            "A2": (420, 594),
            "A1": (594, 841),
            "A0": (841, 1189)
        }
        
        base_width, base_height = paper_ratios.get(paper_size, (210, 297))
        
        # 根据方向调整
        if orientation == "横向":
            base_width, base_height = base_height, base_width
        
        # 为不同纸张设置不同的基础预览尺寸
        if paper_size == "A4":
            max_preview_width, max_preview_height = 280, 400
        elif paper_size == "A3":
            max_preview_width, max_preview_height = 350, 500
        elif paper_size == "A2":
            max_preview_width, max_preview_height = 420, 600
        elif paper_size == "A1":
            max_preview_width, max_preview_height = 480, 680
        elif paper_size == "A0":
            max_preview_width, max_preview_height = 500, 720
        else:
            max_preview_width, max_preview_height = 280, 400
        
        # 保持纸张比例的同时适应最大预览尺寸
        paper_scale_factor = min(max_preview_width / base_width, max_preview_height / base_height)
        preview_width = int(base_width * paper_scale_factor)
        preview_height = int(base_height * paper_scale_factor)
        
        # 创建设计区域的缩略图
        if self.design_area and hasattr(self.design_area, 'graphics_scene'):
            scene = self.design_area.graphics_scene
            
            # 获取场景边界
            scene_rect = scene.itemsBoundingRect()
            if scene_rect.isEmpty():
                scene_rect = scene.sceneRect()
            
            # 创建缩略图
            pixmap = QPixmap(preview_width, preview_height)
            pixmap.fill(Qt.white)
            
            painter = QPainter(pixmap)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 绘制纸张边框
            painter.setPen(QPen(Qt.gray, 2))
            painter.drawRect(0, 0, preview_width-1, preview_height-1)
            
            # 根据比例计算内容区域和缩放
            scale_num = float(self.print_scale.split(':')[1])
            base_margin = max(10, int(preview_width * 0.05))  # 基础页边距
            
            # 比例影响内容大小：比例越大（如1:1000），图形越小
            # 比例越小（如1:50），图形越大
            content_scale_factor = 100.0 / scale_num  # 以1:100为基准
            
            # 计算内容区域大小
            content_width = (preview_width - 2*base_margin) * content_scale_factor
            content_height = (preview_height - 2*base_margin - 40) * content_scale_factor
            
            # 内容区域居中
            content_x = (preview_width - content_width) / 2
            content_y = (preview_height - content_height) / 2 + 20  # 为顶部标题留空间
            
            content_rect = QRectF(content_x, content_y, content_width, content_height)
            
            # 绘制场景到内容区域
            if not scene_rect.isEmpty():
                scene.render(painter, content_rect, scene_rect)
                
                # 如果比例很大，在内容区域周围绘制一个边框来显示实际打印区域
                if scale_num >= 200:
                    painter.setPen(QPen(Qt.blue, 1, Qt.DashLine))
                    painter.drawRect(content_rect)
            
            # 在顶部添加标题信息
            painter.setPen(QPen(Qt.black, 1))
            painter.setFont(QFont("Arial", 8, QFont.Bold))
            title_text = f"打印预览 - {paper_size} {orientation}"
            painter.drawText(10, 15, title_text)
            
            # 在底部添加比例信息
            painter.setFont(QFont("Arial", 7))
            scale_text = f"比例: {self.print_scale} (内容缩放: {content_scale_factor:.2f}x)"
            painter.drawText(10, preview_height - 8, scale_text)
            
            # 在右下角添加尺寸信息
            size_text = f"{preview_width}x{preview_height}px"
            metrics = painter.fontMetrics()
            text_width = metrics.horizontalAdvance(size_text)
            painter.drawText(preview_width - text_width - 10, preview_height - 8, size_text)
            
            painter.end()
            
            self.preview_label.setPixmap(pixmap)
            self.preview_label.setFixedSize(preview_width, preview_height)
            
            print(f"预览更新: {paper_size}({base_width}x{base_height}mm) {orientation} {self.print_scale} -> 预览{preview_width}x{preview_height}px, 内容区域: {content_width:.1f}x{content_height:.1f}, 内容缩放: {content_scale_factor:.2f}")
            
        else:
            self.preview_label.setText(f"无可预览内容\n\n打印设置:\n比例: {self.print_scale}\n纸张: {paper_size} ({base_width}x{base_height}mm)\n方向: {orientation}")
            print(f"无内容预览更新: {paper_size} {orientation} {self.print_scale}")
    
    def print_graphics(self):
        """执行打印"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPrinter.A4)
            
            # 设置打印方向
            if self.orientation_combo.currentText() == "横向":
                printer.setOrientation(QPrinter.Landscape)
            else:
                printer.setOrientation(QPrinter.Portrait)
            
            print_dialog = QPrintDialog(printer, self)
            if print_dialog.exec_() == QPrintDialog.Accepted:
                # 执行打印
                painter = QPainter(printer)
                
                if self.design_area and hasattr(self.design_area, 'graphics_scene'):
                    scene = self.design_area.graphics_scene
                    scene_rect = scene.itemsBoundingRect()
                    if scene_rect.isEmpty():
                        scene_rect = scene.sceneRect()
                    
                    scene.render(painter, printer.pageRect(), scene_rect)
                
                painter.end()
                
                QMessageBox.information(self, "打印完成", "图形已发送到打印机")
                self.accept()
                
        except ImportError:
            QMessageBox.warning(self, "打印错误", "打印功能需要PyQt5打印支持模块")
        except Exception as e:
            QMessageBox.critical(self, "打印失败", f"打印失败：{str(e)}") 