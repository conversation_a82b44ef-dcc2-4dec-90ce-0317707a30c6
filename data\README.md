# 数据导入样本文件说明

本目录包含了Gas Energy Rock Breaking系统支持的各种数据类型的样本文件，用于测试和演示数据导入功能。

## 文件列表

### 1. 炮孔数据 - `sample_holes.csv`
**格式**: CSV (逗号分隔)
**用途**: 导入炮孔的基本信息
**包含字段**:
- 孔号: 炮孔编号 (必需)
- X坐标: 炮孔X坐标 (必需)
- Y坐标: 炮孔Y坐标 (必需)  
- Z坐标: 炮孔Z坐标 (可选，用于爆破水平校验)
- 孔深: 炮孔深度，单位米 (可选，默认15m)
- 备注: 附加说明信息

**数据特点**:
- 包含21个炮孔：15个主炮孔(H001-H015)、2个缓冲孔(B001-B002)、4个周边孔(P001-P004)
- 采用3×5网格布局，间距60m
- Z坐标设为125m，适配默认爆破水平

### 2. 作业面数据 - `sample_work_surface.json`
**格式**: JSON
**用途**: 导入作业面轮廓点
**包含字段**:
- 点号: 作业面点编号
- X坐标: 点的X坐标 (必需)
- Y坐标: 点的Y坐标 (必需)
- 描述: 点的描述信息

**数据特点**:
- 包含10个轮廓点，形成完整的作业面边界
- 覆盖400m×160m的矩形区域

### 3. 边界线数据 - `sample_boundary.txt`
**格式**: TXT (制表符分隔)
**用途**: 导入各种边界线
**包含字段**:
- 点号: 边界点编号
- X坐标: 点的X坐标 (必需)
- Y坐标: 点的Y坐标 (必需)
- 边界类型: 边界线类型分类
- 备注: 附加说明

**数据特点**:
- 包含3层边界：爆区边界、安全边界、警戒边界
- 每层边界都形成闭合区域
- 从内到外逐层扩大安全范围

### 4. 文本标注数据 - `sample_text.csv`
**格式**: CSV (逗号分隔)
**用途**: 导入图纸标注文本
**包含字段**:
- 标注号: 文本标注编号
- X坐标: 文本位置X坐标 (必需)
- Y坐标: 文本位置Y坐标 (必需)
- 文本内容: 要显示的文本 (必需)
- 字体大小: 文本字体大小
- 颜色: 文本颜色

**数据特点**:
- 包含16个文本标注
- 涵盖设计信息、技术参数、区域标识等
- 不同类型采用不同颜色区分

### 5. 参考点数据 - `sample_reference.json`
**格式**: JSON
**用途**: 导入测量控制点
**包含字段**:
- 点号: 参考点编号
- X坐标: 点的X坐标 (必需)
- Y坐标: 点的Y坐标 (必需)
- 标签: 参考点名称
- 类型: 控制点类型
- 精度: 测量精度

**数据特点**:
- 包含10个控制点
- 涵盖主控制点、边界控制点、测量控制点等类型
- 形成完整的测量控制网

### 6. 炸药参数数据 - `sample_explosives.csv`
**格式**: CSV (逗号分隔)
**用途**: 导入炸药参数信息
**包含字段**:
- 炸药名称: 炸药类型名称 (必需)
- 炸药特征: 炸药的特征描述
- 比重(kg/m³): 炸药比重，支持范围值
- 炸药威力(kcal/kg): 炸药威力参数
- 爆速(m/s): 爆炸速度
- 爆热(kcal/kg): 爆炸产生的热量
- 相对质量威力: 相对威力系数
- TNT当量(kg): TNT当量系数
- 可爆性等级: 爆破难易程度

**数据特点**:
- 包含10种常用炸药类型
- 涵盖工业炸药、军用炸药、散装炸药等
- 数值支持单个数值或范围格式（如"750-1000"）

### 7. 打孔设备数据 - `sample_drilling_equipment.csv`
**格式**: CSV (逗号分隔)
**用途**: 导入打孔设备参数信息
**包含字段**:
- 编号: 设备编号 (自动生成)
- 设备名称: 打孔设备名称 (必需)
- 价格(元/m): 每米钻孔的价格 (必需)
- 安全距离(m): 操作安全距离 (必需)
- 孔径(mm): 钻孔直径 (必需)

**数据特点**:
- 包含12种常用打孔设备
- 涵盖凿岩机、钻机、便携设备、重型设备等类型
- 价格范围从25-200元/m，孔径范围从75-300mm
- 安全距离根据设备类型从2-20m不等

## 使用方法

1. **启动程序**: 运行 `python main.py`
2. **选择导入**: 点击"文件" → "数据导入"
3. **选择类型**: 在第一步中选择要导入的数据类型
4. **选择文件**: 在第二步中浏览并选择对应的样本文件
5. **设置映射**: 在第三步中设置列与字段的对应关系
6. **执行导入**: 点击"导入"按钮完成数据导入

## 列映射示例

### 炮孔数据映射:
- 孔号 → 孔号
- X坐标 → X坐标  
- Y坐标 → Y坐标
- Z坐标 → Z坐标
- 孔深 → 孔深

### 文本数据映射:
- X坐标 → X坐标
- Y坐标 → Y坐标
- 文本内容 → 文本内容

### 其他数据类型:
- X坐标 → X坐标
- Y坐标 → Y坐标

## 注意事项

1. **坐标系统**: 所有坐标采用工程坐标系，单位为米
2. **数据格式**: 坐标数据必须为数值类型，文本内容不能为空
3. **爆破水平**: 炮孔Z坐标会进行爆破水平校验，超出范围的数据会被跳过
4. **文件编码**: 建议使用UTF-8编码保存文件，确保中文正常显示
5. **数据顺序**: 边界线和作业面数据的点顺序会影响连线效果

### 8. 岩石参数数据库 - `rock_parameters.json`
**格式**: JSON
**用途**: 系统岩石参数数据库，用于智能布孔计算
**包含字段**:
- 岩石名称: 岩石类型名称
- 岩石特征: 岩石特征描述
- 比重: 岩石比重，支持范围值
- 松散系数: 爆破后的松散系数
- 爆破漏斗: 爆破漏斗系数
- 爆破阻力: 爆破阻力系数
- TNT当量: TNT单耗系数
- 可爆性等级: 爆破难易程度等级

**数据特点**:
- 包含10种常见岩石类型
- 涵盖硬质岩石、中等硬度岩石、软质岩石
- 参数值采用工程实践中的典型范围

### 9. 炸药参数数据库 - `explosive_parameters.json`
**格式**: JSON
**用途**: 系统炸药参数数据库，用于智能布孔计算
**包含字段**:
- 炸药名称: 炸药类型名称
- 炸药特征: 炸药特征描述
- 比重: 炸药比重
- 炸药威力: 炸药威力参数
- 爆速: 爆炸速度
- 爆热: 爆炸热量
- 相对质量威力: 相对威力系数
- TNT当量: TNT当量百分比
- 可爆性等级: 适用爆破等级

**数据特点**:
- 包含10种主要炸药类型
- 涵盖ANFO、乳化炸药、TNT、液氧炸药等
- 参数基于实际工程应用数据

## 自定义数据

您可以参考这些样本文件的格式，创建自己的数据文件：
- 保持相同的文件格式和字段结构
- 确保必需字段不为空
- 坐标数据使用合理的数值范围
- 文本内容避免特殊字符导致的解析错误
- 参数文件支持范围值格式（如"2.6-2.7"） 