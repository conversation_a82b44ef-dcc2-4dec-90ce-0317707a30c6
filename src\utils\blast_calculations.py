# -*- coding: utf-8 -*-
"""
爆破计算工具模块
包含智能布孔、炸药量计算等核心算法
"""

import math
import json
import os

class BlastCalculations:
    """爆破计算类"""
    
    def __init__(self):
        # 工程类型系数
        self.kp_values = {
            "露天矿山": 1.3,
            "地下矿山": 0.95,
            "基坑开挖": 1.0,
            "土石方工程": 1.1
        }
        
        # 布孔模式系数
        self.kb_values = {
            "矩形布孔": 0.8,
            "三角形布孔": 1.0
        }
        
        # 液氧炸药TNT当量表（孔径-TNT当量对照）
        self.liquid_oxygen_tnt = {
            60: 13.3,
            80: 20.8,
            90: 24.6,
            110: 32.1
        }
        
        # 一排填塞长度系数
        self.single_row_k_values = {
            "露天矿山": {"k1": 4, "k2": 0.1},
            "地下矿山": {"k1": 3, "k2": 0.1},
            "基坑开挖": {"k1": 3, "k2": 0.15},
            "土石方工程": {"k1": 2.5, "k2": 0.1}
        }
        
        # 爆区填塞长度系数
        self.blast_area_k_values = {
            "露天矿山": {"k1": 3.5, "k2": 0.15},
            "地下矿山": {"k1": 3, "k2": 0.15},
            "基坑开挖": {"k1": 2.5, "k2": 0.2},
            "土石方工程": {"k1": 2.5, "k2": 0.1}
        }
    
    def get_rock_parameters(self, rock_name):
        """获取岩石参数"""
        try:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            rock_file = os.path.join(data_dir, 'rock_parameters.json')
            
            if os.path.exists(rock_file):
                with open(rock_file, 'r', encoding='utf-8') as f:
                    rock_data = json.load(f)
                
                for rock in rock_data:
                    if rock.get('岩石名称') == rock_name:
                        return rock
        except Exception as e:
            print(f"获取岩石参数时出错: {e}")
        
        return None
    
    def get_explosive_parameters(self, explosive_name):
        """获取炸药参数"""
        try:
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            explosive_file = os.path.join(data_dir, 'explosive_parameters.json')
            
            if os.path.exists(explosive_file):
                with open(explosive_file, 'r', encoding='utf-8') as f:
                    explosive_data = json.load(f)
                
                for explosive in explosive_data:
                    if explosive.get('炸药名称') == explosive_name:
                        return explosive
        except Exception as e:
            print(f"获取炸药参数时出错: {e}")
        
        return None
    
    def parse_range_value(self, value_str):
        """解析范围值，返回均值"""
        if not value_str:
            return 0.0
        
        value_str = str(value_str).strip()
        if '-' in value_str:
            try:
                parts = value_str.split('-')
                min_val = float(parts[0])
                max_val = float(parts[1])
                return (min_val + max_val) / 2
            except:
                return 0.0
        else:
            try:
                return float(value_str)
            except:
                return 0.0
    
    def interpolate_liquid_oxygen_tnt(self, diameter):
        """液氧炸药TNT当量插值计算"""
        diameters = sorted(self.liquid_oxygen_tnt.keys())
        
        # 如果直径在表中，直接返回
        if diameter in self.liquid_oxygen_tnt:
            return self.liquid_oxygen_tnt[diameter]
        
        # 如果小于最小值，返回最小值
        if diameter <= diameters[0]:
            return self.liquid_oxygen_tnt[diameters[0]]
        
        # 如果大于最大值，返回最大值
        if diameter >= diameters[-1]:
            return self.liquid_oxygen_tnt[diameters[-1]]
        
        # 线性插值
        for i in range(len(diameters) - 1):
            if diameters[i] < diameter < diameters[i + 1]:
                x1, y1 = diameters[i], self.liquid_oxygen_tnt[diameters[i]]
                x2, y2 = diameters[i + 1], self.liquid_oxygen_tnt[diameters[i + 1]]
                return y1 + (y2 - y1) * (diameter - x1) / (x2 - x1)
        
        return 0.0
    
    def calculate_hole_spacing_single_row(self, diameter, rock_name, explosive_name, project_type):
        """计算一排布孔的孔距和排距"""
        try:
            # 获取岩石参数
            rock_params = self.get_rock_parameters(rock_name)
            if not rock_params:
                raise ValueError(f"未找到岩石类型 '{rock_name}' 的参数")
            
            # 获取炸药参数
            explosive_params = self.get_explosive_parameters(explosive_name)
            if not explosive_params:
                raise ValueError(f"未找到炸药类型 '{explosive_name}' 的参数")
            
            # 获取岩石相关系数Kr（爆破阻力，取均值）
            kr_str = rock_params.get('爆破阻力', '5.0')
            kr = self.parse_range_value(kr_str)
            
            # 获取炸药相关系数Ke（炸药威力，取均值）
            ke_str = explosive_params.get('炸药威力', '1.0')
            ke = self.parse_range_value(ke_str)
            
            # 获取工程相关系数Kp
            kp = self.kp_values.get(project_type, 1.0)
            
            # 计算孔距 (m) - 使用标准爆破工程公式
            # a = k1 * D + k2 * Lc，其中k1、k2为系数，D为孔径(m)，Lc为装药长度(m)
            # 简化公式：a = (25~35) * D，这里使用30倍孔径作为基础
            hole_spacing = (25 + kr) * (diameter / 1000) * kp
            
            # 计算排距 (m) - 一排布孔排距为孔距的0.6-0.8倍
            row_spacing = 0.7 * hole_spacing
            
            return {
                'hole_spacing': round(hole_spacing, 2),
                'row_spacing': round(row_spacing, 2),
                'kr': kr,
                'ke': ke,
                'kp': kp
            }
            
        except Exception as e:
            raise ValueError(f"计算孔距排距时出错: {e}")
    
    def calculate_hole_spacing_blast_area(self, diameter, rock_name, explosive_name, project_type, layout_mode):
        """计算爆区布孔的孔距和排距"""
        try:
            # 获取岩石参数
            rock_params = self.get_rock_parameters(rock_name)
            if not rock_params:
                raise ValueError(f"未找到岩石类型 '{rock_name}' 的参数")
            
            # 获取炸药参数
            explosive_params = self.get_explosive_parameters(explosive_name)
            if not explosive_params:
                raise ValueError(f"未找到炸药类型 '{explosive_name}' 的参数")
            
            # 获取岩石相关系数Kr（爆破阻力，取均值）
            kr_str = rock_params.get('爆破阻力', '5.0')
            kr = self.parse_range_value(kr_str)
            
            # 获取炸药相关系数Ke（炸药威力，取均值）
            ke_str = explosive_params.get('炸药威力', '1.0')
            ke = self.parse_range_value(ke_str)
            
            # 获取工程相关系数Kp
            kp = self.kp_values.get(project_type, 1.0)
            
            # 获取布孔模式系数Kb
            kb = self.kb_values.get(layout_mode, 0.8)
            
            # 计算孔距 (m) - 使用标准爆破工程公式
            # a = (25~35) * D * Kp，其中D为孔径(m)
            hole_spacing = (25 + kr) * (diameter / 1000) * kp
            
            # 计算排距 (m) - 爆区布孔排距受布孔模式影响
            row_spacing = hole_spacing * kb
            
            return {
                'hole_spacing': round(hole_spacing, 2),
                'row_spacing': round(row_spacing, 2),
                'kr': kr,
                'ke': ke,
                'kp': kp,
                'kb': kb
            }
            
        except Exception as e:
            raise ValueError(f"计算孔距排距时出错: {e}")
    
    def calculate_explosive_amount(self, rock_name, explosive_name, diameter, bench_height, 
                                 row_spacing, hole_spacing, backfill_length, design_depth):
        """计算炸药量和单位长度装药量"""
        try:
            # 获取岩石参数
            rock_params = self.get_rock_parameters(rock_name)
            if not rock_params:
                raise ValueError(f"未找到岩石类型 '{rock_name}' 的参数")
            
            # 获取炸药参数
            explosive_params = self.get_explosive_parameters(explosive_name)
            if not explosive_params:
                raise ValueError(f"未找到炸药类型 '{explosive_name}' 的参数")
            
            # 获取岩石TNT单耗q（取均值）
            q_str = rock_params.get('TNT当量', '0.3')
            q = self.parse_range_value(q_str)
            
            # 获取炸药TNT当量
            if explosive_name == "液氧炸药":
                tnt_equivalent = self.interpolate_liquid_oxygen_tnt(diameter) / 100  # 转换为小数
            else:
                tnt_str = explosive_params.get('TNT当量', '100')
                tnt_equivalent = self.parse_range_value(tnt_str) / 100  # 转换为小数
            
            # 计算炸药量 (kg)
            explosive_amount = q / tnt_equivalent * row_spacing * hole_spacing * bench_height
            
            # 计算装药长度
            charge_length = design_depth - backfill_length
            
            # 计算单位长度装药量 (kg/m)
            unit_charge = explosive_amount / charge_length if charge_length > 0 else 0
            
            # 获取炸药比重
            density_str = explosive_params.get('比重', '1.0')
            explosive_density = self.parse_range_value(density_str)
            
            # 计算最大装药能力 (kg)
            hole_volume = math.pi * (diameter / 2000) ** 2 * charge_length  # 转换mm到m
            max_charge_capacity = hole_volume * explosive_density * 1000  # 转换为kg
            
            # 检验炸药量是否超过最大装药能力
            if explosive_amount > max_charge_capacity:
                raise ValueError("最大装药能力不足，建议调整孔网参数、回填长度等")
            
            return {
                'explosive_amount': round(explosive_amount, 2),
                'unit_charge': round(unit_charge, 2),
                'max_charge_capacity': round(max_charge_capacity, 2),
                'charge_length': round(charge_length, 2),
                'q': q,
                'tnt_equivalent': tnt_equivalent,
                'explosive_density': explosive_density
            }
            
        except Exception as e:
            raise ValueError(f"计算炸药量时出错: {e}")
    
    def calculate_minimum_stemming_single_row(self, project_type, diameter, charge_length, row_spacing):
        """计算一排布孔的最小填塞长度"""
        try:
            # 获取系数
            k_values = self.single_row_k_values.get(project_type)
            if not k_values:
                raise ValueError(f"未找到工程类型 '{project_type}' 的系数")
            
            k1 = k_values['k1']
            k2 = k_values['k2']
            
            # 计算三个条件
            # a. k1 * 孔径 + k2 * 装药长度 (孔径mm转m)
            condition_a = k1 * (diameter / 1000) + k2 * charge_length
            
            # b. 排距的0.95倍
            condition_b = 0.95 * row_spacing
            
            # c. 20倍孔径 (孔径mm转m)
            condition_c = 20 * (diameter / 1000)
            
            # 取最大值
            min_stemming = max(condition_a, condition_b, condition_c)
            
            return {
                'min_stemming': round(min_stemming, 3),
                'condition_a': round(condition_a, 3),
                'condition_b': round(condition_b, 3),
                'condition_c': round(condition_c, 3),
                'k1': k1,
                'k2': k2
            }
            
        except Exception as e:
            raise ValueError(f"计算最小填塞长度时出错: {e}")
    
    def calculate_minimum_stemming_blast_area(self, project_type, diameter, charge_length):
        """计算爆区布孔的最小填塞长度"""
        try:
            # 获取系数
            k_values = self.blast_area_k_values.get(project_type)
            if not k_values:
                raise ValueError(f"未找到工程类型 '{project_type}' 的系数")
            
            k1 = k_values['k1']
            k2 = k_values['k2']
            
            # 计算最小填塞长度 (孔径mm转m)
            min_stemming = k1 * (diameter / 1000) + k2 * charge_length
            
            return {
                'min_stemming': round(min_stemming, 3),
                'k1': k1,
                'k2': k2
            }
            
        except Exception as e:
            raise ValueError(f"计算最小填塞长度时出错: {e}")
    
    def calculate_comprehensive_design(self, params):
        """综合计算智能布孔设计参数"""
        try:
            results = {}
            
            # 根据布孔类型选择计算方法
            if params['layout_type'] == '一排':
                # 计算孔距排距
                spacing_result = self.calculate_hole_spacing_single_row(
                    params['diameter'], params['rock_type'], 
                    params['explosive_type'], params['project_type']
                )
                results.update(spacing_result)
                
                # 计算炸药量
                explosive_result = self.calculate_explosive_amount(
                    params['rock_type'], params['explosive_type'], params['diameter'],
                    params['bench_height'], spacing_result['row_spacing'], 
                    spacing_result['hole_spacing'], params['backfill_length'], 
                    params['design_depth']
                )
                results.update(explosive_result)
                
                # 计算最小填塞长度
                stemming_result = self.calculate_minimum_stemming_single_row(
                    params['project_type'], params['diameter'], 
                    explosive_result['charge_length'], spacing_result['row_spacing']
                )
                results.update(stemming_result)
                
            else:  # 爆区
                # 计算孔距排距
                spacing_result = self.calculate_hole_spacing_blast_area(
                    params['diameter'], params['rock_type'], 
                    params['explosive_type'], params['project_type'], 
                    params['layout_mode']
                )
                results.update(spacing_result)
                
                # 计算炸药量
                explosive_result = self.calculate_explosive_amount(
                    params['rock_type'], params['explosive_type'], params['diameter'],
                    params['bench_height'], spacing_result['row_spacing'], 
                    spacing_result['hole_spacing'], params['backfill_length'], 
                    params['design_depth']
                )
                results.update(explosive_result)
                
                # 计算最小填塞长度
                stemming_result = self.calculate_minimum_stemming_blast_area(
                    params['project_type'], params['diameter'], 
                    explosive_result['charge_length']
                )
                results.update(stemming_result)
            
            return results
            
        except Exception as e:
            raise ValueError(f"综合计算时出错: {e}") 