#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
演示数据生成器
用于在设计区域添加示例数据，方便测试系统功能
"""

import sys
import os
from PyQt5.QtCore import QPointF
from PyQt5.QtWidgets import QApplication, QMessageBox

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def add_demo_data(design_area):
    """添加演示数据到设计区域"""
    
    # 添加炮孔矩阵
    hole_spacing = 50  # 炮孔间距
    rows = 4          # 行数
    cols = 6          # 列数
    start_x = -150    # 起始X坐标
    start_y = -100    # 起始Y坐标
    
    holes = []
    for row in range(rows):
        for col in range(cols):
            x = start_x + col * hole_spacing
            y = start_y + row * hole_spacing
            hole = design_area.add_blast_hole(x, y, 12)
            hole.depth = 15.0 + row * 2  # 深度递增
            hole.rock_type = "花岗岩" if row < 2 else "石灰岩"
            hole.explosive_type = "2号岩石炸药"
            hole.explosive_amount = 25.0 + col * 2
            holes.append(hole)
    
    # 添加作业面
    work_surface_points = [
        QPointF(-200, -150),
        QPointF(150, -150),
        QPointF(150, 100),
        QPointF(-200, 100),
        QPointF(-200, -150)  # 闭合
    ]
    design_area.add_work_surface(work_surface_points)
    
    # 添加边界线
    design_area.add_boundary_line(-180, -130, 130, -130)  # 上边界
    design_area.add_boundary_line(-180, 80, 130, 80)      # 下边界
    design_area.add_boundary_line(-180, -130, -180, 80)   # 左边界
    design_area.add_boundary_line(130, -130, 130, 80)     # 右边界
    
    # 添加文本标注
    design_area.add_text(-50, -170, "作业面A")
    design_area.add_text(80, 120, "安全区域")
    design_area.add_text(-180, 100, "起爆点")
    
    # 添加参考点
    design_area.add_reference_point(0, 0)      # 原点
    design_area.add_reference_point(-180, 80)  # 起爆点
    design_area.add_reference_point(130, -130) # 监测点
    
    # 更新表格数据
    design_area.update_table_data()
    
    return len(holes)

def main():
    """测试函数"""
    from src.main_window import MainWindow
    
    app = QApplication(sys.argv)
    window = MainWindow()
    
    # 添加演示数据
    hole_count = add_demo_data(window.design_area)
    
    # 显示信息
    QMessageBox.information(window, "演示数据", 
                          f"已添加演示数据：\n"
                          f"- {hole_count} 个炮孔\n"
                          f"- 1 个作业面\n"
                          f"- 4 条边界线\n"
                          f"- 3 个文本标注\n"
                          f"- 3 个参考点")
    
    window.show()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 