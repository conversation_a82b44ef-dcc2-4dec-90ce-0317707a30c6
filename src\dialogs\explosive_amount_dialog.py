# -*- coding: utf-8 -*-
"""
炸药量编辑对话框
实现精细化设计中的炸药量编辑功能
"""

import os
import json
import math
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, 
                            QLabel, QLineEdit, QDoubleSpinBox, QComboBox, 
                            QPushButton, QGroupBox, QWidget, QMessageBox,
                            QFormLayout, QSplitter, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont
from ..utils.blast_calculations import BlastCalculations

class CrossSectionWidget(QWidget):
    """炮孔剖面图显示组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(300, 400)
        
        # 剖面图数据
        self.hole_depth = 15.0  # 实测孔深
        self.backfill_length = 3.0  # 回填长度
        self.min_stemming = 2.5  # 最小填塞长度
        self.blast_level = 0.0  # 爆破水平 (炮孔Z坐标)
        self.hole_diameter = 110  # 孔径(mm)
        
    def set_data(self, hole_depth, backfill_length, min_stemming, blast_level, hole_diameter):
        """设置剖面图数据"""
        self.hole_depth = hole_depth
        self.backfill_length = backfill_length
        self.min_stemming = min_stemming
        self.blast_level = blast_level # Z坐标
        self.hole_diameter = hole_diameter
        self.update()
    
    def paintEvent(self, event):
        """绘制剖面图"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制区域尺寸
        width = self.width() - 40
        height = self.height() - 80
        start_x = 20
        start_y = 40
        
        # 计算比例尺
        total_height = max(self.hole_depth + 5, 20)  # 确保有足够空间
        scale = height / total_height
        
        # 孔径宽度（像素）
        hole_width = max(20, self.hole_diameter / 10)  # 孔径转换为显示宽度
        hole_center_x = start_x + width // 2
        
        # 绘制背景网格
        painter.setPen(QPen(QColor(230, 230, 230), 1))
        for i in range(0, int(total_height), 2):
            y = int(start_y + i * scale)
            painter.drawLine(start_x, y, start_x + width, y)
        
        # 绘制地面线
        ground_y = int(start_y)
        painter.setPen(QPen(QColor(139, 69, 19), 3))  # 棕色地面线
        painter.drawLine(start_x, ground_y, start_x + width, ground_y)
        
        # 绘制孔壁
        hole_left = hole_center_x - hole_width // 2
        hole_right = hole_center_x + hole_width // 2
        hole_bottom_y = int(start_y + self.hole_depth * scale)
        
        painter.setPen(QPen(Qt.black, 2))
        painter.drawLine(hole_left, ground_y, hole_left, hole_bottom_y)  # 左壁
        painter.drawLine(hole_right, ground_y, hole_right, hole_bottom_y)  # 右壁
        painter.drawLine(hole_left, hole_bottom_y, hole_right, hole_bottom_y)  # 底部
        
        # 绘制回填段（黑色矩形）
        if self.backfill_length > 0:
            backfill_height = int(self.backfill_length * scale)
            painter.setBrush(QBrush(Qt.black))
            painter.setPen(QPen(Qt.black, 1))
            painter.drawRect(hole_left, ground_y, hole_width, backfill_height)
        
        # 绘制装药段（黄色矩形）
        charge_length = self.hole_depth - self.backfill_length
        if charge_length > 0:
            charge_top_y = int(start_y + self.backfill_length * scale)
            charge_height = int(charge_length * scale)
            painter.setBrush(QBrush(Qt.yellow))
            painter.setPen(QPen(Qt.darkYellow, 1))
            painter.drawRect(hole_left, charge_top_y, hole_width, charge_height)
        
        # 绘制最小填塞长度警示线（红色）
        if self.min_stemming > 0 and self.min_stemming <= self.hole_depth:
            min_stemming_y = int(start_y + self.min_stemming * scale)
            painter.setPen(QPen(Qt.red, 2, Qt.DashLine))
            painter.drawLine(start_x, min_stemming_y, start_x + width, min_stemming_y)
            
            # 标注最小填塞长度
            painter.setPen(QPen(Qt.red, 1))
            painter.setFont(QFont("Arial", 8))
            painter.drawText(start_x + width - 120, min_stemming_y - 5, "最小填塞长度")
        
        # 绘制标高标注
        painter.setPen(QPen(Qt.black, 1))
        painter.setFont(QFont("Arial", 9))
        
        # 操作标高（孔底标高）
        operation_level = self.blast_level - self.hole_depth
        operation_y = int(start_y + self.hole_depth * scale)
        painter.drawText(hole_right + 10, operation_y + 5, f"操作标高: {operation_level:.1f}m")
        
        # 0米刻度标注 (Z坐标)
        design_level_y = int(start_y)
        painter.drawText(hole_right + 10, design_level_y - 5, f"Z坐标: {self.blast_level:.1f}m")
        
        # 深度标注
        depth_x = hole_left - 40
        for i in range(0, int(self.hole_depth) + 1, 2):
            y = int(start_y + i * scale)
            painter.drawLine(depth_x, y, depth_x + 10, y)
            painter.drawText(depth_x - 30, y + 3, f"{i}m")
        
        # 绘制孔径标注
        painter.setPen(QPen(Qt.blue, 1))
        painter.drawText(hole_center_x - 20, hole_bottom_y + 20, f"φ{self.hole_diameter}mm")


class ExplosiveAmountDialog(QDialog):
    """炸药量编辑对话框"""
    
    # 数据更新信号
    data_updated = pyqtSignal(dict)
    
    def __init__(self, hole_item, main_window, parent=None):
        super().__init__(parent)
        self.hole_item = hole_item
        self.main_window = main_window
        self.calculations = BlastCalculations()
        
        # 初始化数据
        self.init_hole_data()
        self.init_ui()
        self.load_data()
        self.connect_signals()
        
        # 用户是否手动修改了炸药量
        self.explosive_amount_manually_set = False
        
        # 最小填塞长度
        self.min_stemming = 2.5
    
    def init_hole_data(self):
        """初始化炮孔数据"""
        # 从炮孔对象获取基础数据
        self.hole_id = getattr(self.hole_item, 'hole_id', 'H001')
        self.hole_type = getattr(self.hole_item, 'special_hole_type', '起爆孔')
        self.measured_depth = getattr(self.hole_item, 'depth', 15.0)
        self.explosive_type = getattr(self.hole_item, 'explosive_type', '乳化炸药')
        self.rock_type = getattr(self.hole_item, 'rock_type', '花岗岩')
        self.hole_diameter = getattr(self.hole_item, 'diameter_value', 110.0)
        self.backfill_length = getattr(self.hole_item, 'backfill_length', 3.0)
        self.explosive_amount = getattr(self.hole_item, 'explosive_amount', 25.0)
        self.hole_spacing = getattr(self.hole_item, 'spacing', 3.5)
        self.row_spacing = getattr(self.hole_item, 'row_spacing', 4.0)
        
        # 从主窗口获取设计参数
        self.blast_level = getattr(self.main_window, 'blast_level', 0.0)
        self.step_height = getattr(self.main_window, 'step_height', 15.0)
        
        # Z坐标（如果有的话）
        self.z_coord = getattr(self.hole_item, 'z_coord', self.blast_level)
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(f"编辑炸药量 - {self.hole_id}")
        self.setModal(True)
        self.resize(800, 600)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：剖面图
        self.cross_section = CrossSectionWidget()
        left_frame = QFrame()
        left_frame.setFrameStyle(QFrame.StyledPanel)
        left_layout = QVBoxLayout(left_frame)
        left_layout.addWidget(QLabel("炮孔垂直剖面图"))
        left_layout.addWidget(self.cross_section)
        splitter.addWidget(left_frame)
        
        # 右侧：数据编辑区
        right_widget = self.create_data_edit_widget()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.calculate_button = QPushButton("重新计算")
        self.calculate_button.clicked.connect(self.recalculate_explosive_amount)
        button_layout.addWidget(self.calculate_button)
        
        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept_changes)
        button_layout.addWidget(self.ok_button)
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
    
    def create_data_edit_widget(self):
        """创建数据编辑区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 1. 选中炮孔信息组 (只读)
        info_group = QGroupBox("选中炮孔的信息")
        info_layout = QFormLayout()
        
        self.info_hole_id = QLabel()
        self.info_coords = QLabel()
        self.info_hole_type = QLabel()
        self.info_diameter = QLabel()
        self.info_rock_type = QLabel()
        self.info_explosive_type = QLabel()
        self.info_spacing = QLabel()
        self.info_row_spacing = QLabel()

        info_layout.addRow("炮孔编号:", self.info_hole_id)
        coords_text = f"({self.hole_item.pos().x():.1f}, {self.hole_item.pos().y():.1f}, {self.z_coord:.1f})"
        self.info_coords.setText(coords_text)
        self.info_hole_type.setText(self.hole_type)
        self.info_diameter.setText(f"{self.hole_diameter:.0f} mm")
        self.info_rock_type.setText(self.rock_type)
        self.info_explosive_type.setText(self.explosive_type)
        self.info_spacing.setText(f"{self.hole_spacing:.1f} m")
        self.info_row_spacing.setText(f"{self.row_spacing:.1f} m")
        
        info_group.setLayout(info_layout)
        layout.addWidget(info_group)
        
        # 2. 编辑参数组
        edit_group = QGroupBox("编辑参数")
        edit_layout = QFormLayout()

        self.measured_depth_spinbox = QDoubleSpinBox()
        self.measured_depth_spinbox.setRange(1.0, 50.0)
        self.measured_depth_spinbox.setDecimals(1)
        self.measured_depth_spinbox.setSuffix(" 米")
        edit_layout.addRow("实测孔深:", self.measured_depth_spinbox)

        self.project_type_combo = QComboBox()
        self.project_type_combo.addItems(["露天矿山剥离爆破", "地下矿山开采爆破", "基坑开挖爆破", "土石方工程爆破"])
        edit_layout.addRow("工程类型:", self.project_type_combo)

        self.backfill_length_spinbox = QDoubleSpinBox()
        self.backfill_length_spinbox.setRange(0.0, 20.0)
        self.backfill_length_spinbox.setDecimals(1)
        self.backfill_length_spinbox.setSuffix(" 米")
        edit_layout.addRow("回填长度:", self.backfill_length_spinbox)
        
        edit_group.setLayout(edit_layout)
        layout.addWidget(edit_group)

        # 3. 炸药参数组 (不变)
        explosive_group = QGroupBox("炸药参数")
        explosive_layout = QFormLayout()
        
        self.explosive_amount_spinbox = QDoubleSpinBox()
        self.explosive_amount_spinbox.setRange(0.1, 200.0)
        self.explosive_amount_spinbox.setDecimals(2)
        self.explosive_amount_spinbox.setSuffix(" 千克")
        explosive_layout.addRow("炸药量:", self.explosive_amount_spinbox)
        
        self.unit_charge_label = QLabel("0.00 kg/m")
        explosive_layout.addRow("单位长度装药量:", self.unit_charge_label)
        
        self.max_capacity_label = QLabel("0.00 kg")
        explosive_layout.addRow("最大装药能力:", self.max_capacity_label)
        
        explosive_group.setLayout(explosive_layout)
        layout.addWidget(explosive_group)
        
        layout.addStretch()
        widget.setLayout(layout)
        return widget
    
    def connect_signals(self):
        """连接信号"""
        self.measured_depth_spinbox.valueChanged.connect(self.on_data_changed)
        self.backfill_length_spinbox.valueChanged.connect(self.on_data_changed)
        self.explosive_amount_spinbox.valueChanged.connect(self.on_explosive_amount_changed)
        self.project_type_combo.currentTextChanged.connect(self.on_data_changed)
    
    def load_data(self):
        """加载初始数据"""
        # 填充只读信息
        self.info_hole_id.setText(self.hole_id)
        coords_text = f"({self.hole_item.pos().x():.1f}, {self.hole_item.pos().y():.1f}, {self.z_coord:.1f})"
        self.info_coords.setText(coords_text)
        self.info_hole_type.setText(self.hole_type)
        self.info_diameter.setText(f"{self.hole_diameter:.0f} mm")
        self.info_rock_type.setText(self.rock_type)
        self.info_explosive_type.setText(self.explosive_type)
        self.info_spacing.setText(f"{self.hole_spacing:.1f} m")
        self.info_row_spacing.setText(f"{self.row_spacing:.1f} m")

        # 填充可编辑信息
        self.measured_depth_spinbox.setValue(self.measured_depth)
        self.backfill_length_spinbox.setValue(self.backfill_length)
        self.explosive_amount_spinbox.setValue(self.explosive_amount)
        project_type = getattr(self.hole_item, 'project_type', "露天矿山剥离爆破")
        index = self.project_type_combo.findText(project_type)
        if index >= 0:
            self.project_type_combo.setCurrentIndex(index)

        self.update_calculations()
        self.update_cross_section()
    
    def on_data_changed(self):
        """数据变化时的处理"""
        # 验证回填长度
        if self.backfill_length_spinbox.value() > self.measured_depth_spinbox.value():
            QMessageBox.warning(self, "参数错误", "回填长度不能大于实测孔深！")
            self.backfill_length_spinbox.setValue(self.measured_depth_spinbox.value())
            return
        
        # 如果炸药量未被手动修改，则重新计算
        if not self.explosive_amount_manually_set:
            self.recalculate_explosive_amount()
        
        self.update_calculations()
        self.update_cross_section()
    
    def on_explosive_amount_changed(self):
        """炸药量变化时的处理"""
        self.explosive_amount_manually_set = True
        self.update_calculations()
        self.check_max_capacity()
    
    def recalculate_explosive_amount(self):
        """重新计算炸药量"""
        try:
            # 获取当前参数
            rock_name = self.rock_type
            explosive_name = self.explosive_type
            diameter = self.hole_diameter
            bench_height = self.step_height
            row_spacing = self.row_spacing
            hole_spacing = self.hole_spacing
            backfill_length = self.backfill_length_spinbox.value()
            design_depth = self.measured_depth_spinbox.value()
            
            # 使用爆破计算类计算炸药量
            result = self.calculations.calculate_explosive_amount(
                rock_name, explosive_name, diameter, bench_height,
                row_spacing, hole_spacing, backfill_length, design_depth
            )
            
            # 更新炸药量
            self.explosive_amount_spinbox.setValue(result['explosive_amount'])
            self.explosive_amount_manually_set = False
            
        except Exception as e:
            QMessageBox.warning(self, "计算错误", f"炸药量计算失败:\n{str(e)}")
    
    def update_calculations(self):
        """更新计算结果"""
        try:
            # 计算装药长度
            charge_length = self.measured_depth_spinbox.value() - self.backfill_length_spinbox.value()
            
            # 计算单位长度装药量
            if charge_length > 0:
                unit_charge = self.explosive_amount_spinbox.value() / charge_length
            else:
                unit_charge = 0
            
            self.unit_charge_label.setText(f"{unit_charge:.2f} kg/m")
            
            # 计算最大装药能力
            self.calculate_max_capacity()
            
            # 计算最小填塞长度
            self.calculate_min_stemming()
            
        except Exception as e:
            print(f"更新计算结果时出错: {e}")
    
    def calculate_max_capacity(self):
        """计算最大装药能力"""
        try:
            # 获取炸药参数
            explosive_params = self.calculations.get_explosive_parameters(self.explosive_type)
            if explosive_params:
                density_str = explosive_params.get('比重', '1.0')
                explosive_density = self.calculations.parse_range_value(density_str)
            else:
                explosive_density = 1.0
            
            # 计算装药体积
            diameter_m = self.hole_diameter / 1000
            charge_length = self.measured_depth_spinbox.value() - self.backfill_length_spinbox.value()
            hole_volume = math.pi * (diameter_m / 2) ** 2 * charge_length
            
            # 计算最大装药能力
            max_capacity = hole_volume * explosive_density * 1000  # 转换为kg
            self.max_capacity_label.setText(f"{max_capacity:.2f} kg")
            
        except Exception as e:
            self.max_capacity_label.setText("计算错误")
            print(f"计算最大装药能力时出错: {e}")
    
    def calculate_min_stemming(self):
        """计算最小填塞长度"""
        try:
            project_type = self.project_type_combo.currentText()
            
            # 获取系数
            if project_type == "露天矿山剥离爆破":
                k1, k2 = 3.5, 0.15
            elif project_type == "地下矿山开采爆破":
                k1, k2 = 3.0, 0.15
            elif project_type == "基坑开挖爆破":
                k1, k2 = 2.5, 0.20
            else:  # 土石方工程爆破
                k1, k2 = 2.5, 0.10
            
            # 计算最小填塞长度
            diameter_m = self.hole_diameter / 1000
            charge_length = self.measured_depth_spinbox.value() - self.backfill_length_spinbox.value()
            self.min_stemming = k1 * diameter_m + k2 * charge_length
            
        except Exception as e:
            self.min_stemming = 2.0
            print(f"计算最小填塞长度时出错: {e}")
    
    def check_max_capacity(self):
        """检查最大装药能力"""
        try:
            max_capacity_text = self.max_capacity_label.text().replace(" kg", "")
            max_capacity = float(max_capacity_text)
            current_amount = self.explosive_amount_spinbox.value()
            
            if current_amount > max_capacity:
                QMessageBox.warning(self, "装药能力不足", 
                                  f"当前炸药量({current_amount:.2f}kg)超过最大装药能力({max_capacity:.2f}kg)！\n"
                                  f"建议调整孔网参数、回填长度等。")
                # 将炸药量输入框边框设为红色
                self.explosive_amount_spinbox.setStyleSheet("border: 2px solid red;")
            else:
                # 恢复正常样式
                self.explosive_amount_spinbox.setStyleSheet("")
                
        except Exception as e:
            print(f"检查最大装药能力时出错: {e}")
    
    def update_cross_section(self):
        """更新剖面图"""
        hole_depth = self.measured_depth_spinbox.value()
        backfill_length = self.backfill_length_spinbox.value()
        
        # 检查回填长度是否符合最小填塞长度要求
        if backfill_length < self.min_stemming:
            # 如果回填长度不足，剖面图会用红色警示线显示
            pass
        
        self.cross_section.set_data(
            hole_depth, 
            backfill_length, 
            self.min_stemming,
            self.z_coord,
            self.hole_diameter
        )
    
    def accept_changes(self):
        """接受修改"""
        try:
            # 最终验证
            if self.backfill_length_spinbox.value() > self.measured_depth_spinbox.value():
                QMessageBox.warning(self, "参数错误", "回填长度不能大于实测孔深！")
                return
            
            # 准备数据
            updated_data = {
                'measured_depth': self.measured_depth_spinbox.value(),
                'backfill_length': self.backfill_length_spinbox.value(),
                'explosive_amount': self.explosive_amount_spinbox.value(),
                'project_type': self.project_type_combo.currentText(),
                'unit_charge': float(self.unit_charge_label.text().replace(" kg/m", "")),
                'min_stemming': self.min_stemming
            }
            
            # 发射数据更新信号
            self.data_updated.emit(updated_data)
            
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存数据时出错:\n{str(e)}") 