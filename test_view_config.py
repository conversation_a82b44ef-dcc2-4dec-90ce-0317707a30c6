#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试查看配置功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from src.main_window import MainWindow

def test_view_config():
    """测试查看配置功能"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 添加一些测试数据
    print("添加测试炮孔...")
    hole1 = main_window.design_area.add_blast_hole(100, 100, 110)
    hole1.hole_id = "H001"
    hole1.hole_type = "主炮孔"
    hole1.rock_type = "花岗岩"
    hole1.explosive_type = "铵梯炸药"
    
    hole2 = main_window.design_area.add_blast_hole(200, 100, 110)
    hole2.hole_id = "H002"
    hole2.hole_type = "缓冲孔"
    hole2.rock_type = "石灰岩"
    hole2.explosive_type = "乳化炸药"
    
    # 添加边界线
    print("添加测试边界线...")
    main_window.design_area.add_boundary_line(50, 50, 250, 50)
    main_window.design_area.add_boundary_line(250, 50, 250, 150)
    
    # 添加文本
    print("添加测试文本...")
    main_window.design_area.add_text(150, 50, "测试文本")
    
    # 添加参考点
    print("添加测试参考点...")
    main_window.design_area.add_reference_point(50, 150)
    
    # 更新炮孔显示
    print("更新炮孔标签...")
    for hole in [hole1, hole2]:
        hole.update_text_label()
        main_window.update_hole_label_display(hole)
    
    print("测试数据添加完成")
    print("\n=== 查看配置测试 ===")
    print("1. 点击菜单 '编辑' -> '查看' 或工具栏的查看按钮")
    print("2. 在对话框中勾选/取消勾选不同选项")
    print("3. 点击'应用'查看界面变化")
    print("4. 测试以下功能：")
    print("   - 网格显示/隐藏")
    print("   - 炮孔信息显示控制（孔号、坐标、孔深、孔径等）")
    print("   - 边界线显示/隐藏")
    print("   - 文本标注显示/隐藏")
    print("   - 参考点显示/隐藏")
    
    # 测试应用显示设置
    print("\n测试隐藏所有网格...")
    main_window.view_config['grid'] = False
    main_window.apply_display_settings()
    
    print("测试隐藏孔深信息...")
    main_window.view_config['hole_depth'] = False
    main_window.apply_display_settings()
    
    print("测试显示坐标信息...")
    main_window.view_config['coordinates'] = True
    main_window.apply_display_settings()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    test_view_config() 