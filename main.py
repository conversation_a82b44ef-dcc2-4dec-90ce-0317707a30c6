#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gas Energy Rock Breaking System
燃气能量岩石破碎系统主程序
"""

import sys
from PyQt5.QtWidgets import QApplication, QDialog
from PyQt5.QtGui import QFont

from src.main_window import MainWindow
from src.dialogs.login_dialog import LoginDialog

if __name__ == '__main__':
    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 显示登录对话框
    login_dialog = LoginDialog()
    if login_dialog.exec_() == QDialog.Accepted:
        # 登录成功，显示主窗口
        
        # 设置应用程序属性
        app.setApplicationName("Gas Energy Rock Breaking")
        app.setApplicationVersion("1.0.0")
        app.setOrganizationName("岩石爆破设计系统")
        
        # 设置全局字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 设置样式
        app.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QMenuBar {
                background-color: #e0e0e0;
                border: 1px solid #c0c0c0;
            }
            QMenuBar::item {
                padding: 4px 8px;
                background-color: transparent;
            }
            QMenuBar::item:selected {
                background-color: #d0d0d0;
            }
            QToolBar {
                background-color: #e8e8e8;
                border: 1px solid #c0c0c0;
                spacing: 2px;
            }
            QStatusBar {
                background-color: #e0e0e0;
                border-top: 1px solid #c0c0c0;
            }
            QTreeWidget {
                background-color: white;
                border: 1px solid #c0c0c0;
                font-size: 9pt;
            }
            QTreeWidget::item {
                padding: 2px;
            }
            QTreeWidget::item:selected {
                background-color: #3399ff;
                color: white;
            }
            QPushButton {
                background-color: #f8f8f8;
                border: 1px solid #c0c0c0;
                padding: 4px 8px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #e8e8e8;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        
        main_win = MainWindow()
        main_win.show()
        sys.exit(app.exec_())
    else:
        # 用户取消登录，退出程序
        sys.exit(0) 