#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
炸药精细化设计对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QComboBox, QTextEdit, QPushButton, QGroupBox,
                            QGridLayout, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap

class ExplosiveDesignDialog(QDialog):
    """炸药精细化设计对话框"""
    
    def __init__(self, explosive_data_list, parent=None):
        super().__init__(parent)
        self.explosive_data = explosive_data_list
        self.selected_explosive_type = ""
        self.selected_charge_structure = "连续装药"
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("炸药精细化设计")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("炸药类型与装药结构设置")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # 炸药类型选择组
        explosive_group = QGroupBox("炸药类型")
        explosive_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        explosive_layout = QVBoxLayout()
        
        # 炸药类型说明
        explosive_info = QLabel("选择适合当前爆破作业的炸药类型：")
        explosive_info.setStyleSheet("color: #7f8c8d; margin-bottom: 5px;")
        explosive_layout.addWidget(explosive_info)
        
        # 炸药类型下拉框
        self.explosive_combo = QComboBox()
        explosive_names = [exp.get("炸药名称", "未知") for exp in self.explosive_data]
        self.explosive_combo.addItems(explosive_names)
        
        self.explosive_combo.currentTextChanged.connect(self.on_explosive_changed)
        
        # 增加下拉框的高度和样式设置
        self.explosive_combo.setMinimumHeight(35)  # 设置最小高度
        self.explosive_combo.setMaxVisibleItems(8)  # 设置最大可见项目数
        self.explosive_combo.setStyleSheet("""
            QComboBox {
                padding: 10px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                font-size: 11pt;
                min-height: 25px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
                min-height: 25px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 20px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e8f4fd;
            }
        """)
        explosive_layout.addWidget(self.explosive_combo)
        
        explosive_group.setLayout(explosive_layout)
        main_layout.addWidget(explosive_group)
        
        # 装药结构选择组
        structure_group = QGroupBox("装药结构")
        structure_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        structure_layout = QVBoxLayout()
        
        # 装药结构说明
        structure_info = QLabel("选择装药结构类型：")
        structure_info.setStyleSheet("color: #7f8c8d; margin-bottom: 5px;")
        structure_layout.addWidget(structure_info)
        
        # 装药结构下拉框
        self.structure_combo = QComboBox()
        self.structure_combo.addItems([
            "连续装药",
            "分段装药"
        ])
        self.structure_combo.setCurrentText(self.selected_charge_structure)
        self.structure_combo.currentTextChanged.connect(self.on_structure_changed)
        
        # 同样增加装药结构下拉框的高度和样式设置
        self.structure_combo.setMinimumHeight(35)  # 设置最小高度
        self.structure_combo.setMaxVisibleItems(5)  # 设置最大可见项目数
        self.structure_combo.setStyleSheet("""
            QComboBox {
                padding: 10px;
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                font-size: 11pt;
                min-height: 25px;
            }
            QComboBox:focus {
                border: 2px solid #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #bdc3c7;
                selection-background-color: #3498db;
                selection-color: white;
                background-color: white;
                min-height: 25px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 20px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e8f4fd;
            }
        """)
        structure_layout.addWidget(self.structure_combo)
        
        structure_group.setLayout(structure_layout)
        main_layout.addWidget(structure_group)
        
        # 参数说明区域
        info_group = QGroupBox("参数说明")
        info_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                margin: 5px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        info_layout = QVBoxLayout()
        
        self.info_text = QTextEdit()
        # 增加参数说明区域的高度：从80改为120
        self.info_text.setMaximumHeight(120)
        self.info_text.setReadOnly(True)
        self.info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                padding: 8px;
                font-size: 10pt;
                line-height: 1.4;
            }
        """)
        self.update_info_text()
        info_layout.addWidget(self.info_text)
        
        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.ok_button = QPushButton("确定")
        # 增加按钮大小
        self.ok_button.setFixedSize(100, 35)
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        self.ok_button.clicked.connect(self.accept)
        
        self.cancel_button = QPushButton("取消")
        # 增加按钮大小
        self.cancel_button.setFixedSize(100, 35)
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)
    
    def on_explosive_changed(self, explosive_type):
        """炸药类型改变时的处理"""
        self.selected_explosive_type = explosive_type
        self.update_info_text()
    
    def on_structure_changed(self, structure):
        """装药结构改变时的处理"""
        self.selected_charge_structure = structure
        self.update_info_text()
    
    def update_info_text(self):
        """更新参数说明文本"""
        # 从列表中查找当前选中的炸药信息
        current_explosive_info = next((item for item in self.explosive_data if item.get("炸药名称") == self.selected_explosive_type), None)
        
        explosive_description = "无详细信息"
        if current_explosive_info:
            explosive_description = current_explosive_info.get("适用场景", "无详细信息")

        structure_info = {
            "连续装药": "炸药连续填充整个炮孔，爆破效果均匀，适用于一般爆破",
            "分段装药": "炸药分段间隔装填，可精确控制爆破时序，适用于精密爆破"
        }
        
        info_text = f"炸药类型：{explosive_description}\n"
        info_text += f"装药结构：{structure_info.get(self.selected_charge_structure, '无详细信息')}"
        
        self.info_text.setPlainText(info_text)
    
    def get_selection(self):
        """获取用户选择"""
        return {
            'explosive_type': self.selected_explosive_type,
            'charge_structure': self.selected_charge_structure
        } 