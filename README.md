# Gas Energy Rock Breaking System
燃气能量岩石破碎系统

## 简介
专业的岩石爆破设计软件，提供炮孔设计、参数管理、数据导入等功能。

## 运行方式
```bash
python main.py
```

或双击运行：
- `run.bat` - 正常运行
- `run_demo.bat` - 演示模式（自动加载示例数据）

## 主要功能

### 基础功能
- **文件管理**：新建、打开、保存、数据导入、图形打印
- **参数设置**：岩石参数、炸药参数、打孔设备管理
- **设计工具**：炮孔设计、精细化设计、图形绘制
- **输出功能**：爆破网络图、参数表导出

### 智能布孔功能 🆕
- **一排布孔**：根据工程参数自动计算孔距，在指定线段上均匀分布炮孔
- **爆区布孔**：支持矩形和三角形布孔模式，自动生成网格化布孔方案
- **参数计算**：基于爆破理论公式智能计算孔距、排距、炸药量等关键参数
- **实时示意图**：左侧显示炮孔结构示意图，右侧提供参数设置和结果显示
- **参数验证**：自动检查设计参数合理性，确保爆破安全

详细使用说明请参考：[智能布孔功能使用指南](INTELLIGENT_LAYOUT_GUIDE.md)

## 文件格式
系统使用JSON格式保存设计文件，包含以下结构：
- `design_info`: 设计基本信息（名称、爆破水平、台阶段高）
- `holes`: 炮孔数据数组
- `boundaries`: 边界线数据数组
- `texts`: 文本标注数组
- `references`: 参考点数组

示例文件：`sample_design.json`

## 系统要求
- Python 3.6+
- PyQt5