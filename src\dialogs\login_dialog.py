import sys
from PyQt5.QtWidgets import (QApplication, QDialog, QLabel, QLineEdit, QPushButton,
                             QVBoxLayout, QHBoxLayout, QWidget, QMessageBox)
from PyQt5.QtGui import QPixmap, QPalette, QBrush, QIcon, QFont
from PyQt5.QtCore import Qt

from src.utils.resource_helper import resource_path


class LoginDialog(QDialog):
    """
    登录对话框，处理用户登录。
    """
    def __init__(self, parent=None):
        super(LoginDialog, self).__init__(parent)
        self.init_ui()

    def init_ui(self):
        """
        初始化UI界面。
        """
        self.setWindowTitle("气能破岩参数设计云平台 - 登录")
        self.setWindowIcon(QIcon(resource_path("image/logo.ico")))
        self.setFixedSize(1200, 700)

        # 设置背景图片
        palette = QPalette()
        pixmap = QPixmap(resource_path("image/login.png"))
        palette.setBrush(QPalette.Window, QBrush(pixmap.scaled(
            self.size(), Qt.IgnoreAspectRatio, Qt.SmoothTransformation)))
        self.setPalette(palette)

        # 主垂直布局
        main_v_layout = QVBoxLayout(self)
        main_v_layout.setContentsMargins(0, 0, 0, 20)
        main_v_layout.addStretch(1)

        # 中间内容区域的水平布局
        content_h_layout = QHBoxLayout()
        content_h_layout.addStretch(3)  # 左侧弹簧

        # 登录表单
        form_widget = QWidget()
        form_widget.setFixedSize(380, 380)
        form_widget.setStyleSheet("""
            QWidget {
                background-color: rgba(25, 25, 25, 0.8);
                border-radius: 20px;
            }
        """)
        form_layout = QVBoxLayout(form_widget)
        form_layout.setContentsMargins(40, 40, 40, 40)
        form_layout.setSpacing(20)

        login_title = QLabel("用户登录")
        login_title.setFont(QFont("黑体", 20, QFont.Bold))
        login_title.setStyleSheet("color: white; background-color: transparent;")
        login_title.setAlignment(Qt.AlignCenter)

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("用户名")
        self.username_input.setFixedHeight(45)
        self.username_input.setStyleSheet("""
            QLineEdit {
                background-color: rgba(0, 0, 0, 0.3);
                border: 1px solid #444;
                border-radius: 8px;
                padding-left: 15px;
                color: white;
                font-size: 16px;
            }
        """)

        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("密码")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFixedHeight(45)
        self.password_input.setStyleSheet("""
            QLineEdit {
                background-color: rgba(0, 0, 0, 0.3);
                border: 1px solid #444;
                border-radius: 8px;
                padding-left: 15px;
                color: white;
                font-size: 16px;
            }
        """)

        login_button = QPushButton("登 录")
        login_button.setFixedHeight(50)
        login_button.setFont(QFont("黑体", 16, QFont.Bold))
        login_button.setStyleSheet("""
            QPushButton {
                background-color: #3474E1;
                color: white;
                border-radius: 8px;
            }
            QPushButton:hover {
                background-color: #4C88F7;
            }
            QPushButton:pressed {
                background-color: #2A5FB5;
            }
        """)
        login_button.clicked.connect(self.handle_login)
        
        form_layout.addWidget(login_title)
        form_layout.addWidget(self.username_input)
        form_layout.addWidget(self.password_input)
        form_layout.addWidget(login_button)

        content_h_layout.addWidget(form_widget)
        content_h_layout.addStretch(1)  # 右侧弹簧

        # 左侧软件标题
        title_v_layout = QVBoxLayout()
        app_title = QLabel("气能破岩参数设计云平台")
        app_title.setFont(QFont("黑体", 32, QFont.Bold))
        app_title.setStyleSheet("color: white; background: transparent;")
        app_title.setAlignment(Qt.AlignCenter)
        title_v_layout.addStretch(1)
        title_v_layout.addWidget(app_title)
        title_v_layout.addStretch(1)

        # 将标题和表单添加到主布局中
        main_content_h_layout = QHBoxLayout()
        main_content_h_layout.setContentsMargins(50, 0, 50, 0)
        main_content_h_layout.addLayout(title_v_layout, 2)
        main_content_h_layout.addLayout(content_h_layout, 2)

        main_v_layout.addLayout(main_content_h_layout)
        main_v_layout.addStretch(1)

        # 底部公司信息
        company_label = QLabel("广东宏凯气能技术有限公司 | 联系电话：xxxxxx")
        company_label.setFont(QFont("黑体", 12))
        company_label.setStyleSheet("color: #DDD; background: transparent;")
        company_label.setAlignment(Qt.AlignCenter)
        main_v_layout.addWidget(company_label)

    def handle_login(self):
        """
        处理登录逻辑。
        """
        # 在实际应用中,应该与后端或数据库进行验证
        username = self.username_input.text()
        password = self.password_input.text()

        if username == "admin" and password == "admin":
            self.accept()
        else:
            QMessageBox.warning(self, "登录失败", "用户名或密码错误！")


if __name__ == '__main__':
    # 用于独立测试登录对话框
    app = QApplication(sys.argv)
    dialog = LoginDialog()
    # dialog.show() # 使用show()是非模态的，这里我们需要模态
    if dialog.exec_() == QDialog.Accepted:
        print("登录成功!")
        # 这里可以启动主窗口
        # main_win = MainWindow()
        # main_win.show()
        # sys.exit(app.exec_())
    else:
        print("取消登录。")
        sys.exit(0) 