#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精细化设计功能演示脚本
展示如何使用精细化设计的各个功能
"""

print("精细化设计功能使用指南")
print("=" * 50)

print("\n1. 孔类型设置(起爆孔):")
print("   - 菜单路径: 设计 -> 精细化设计 -> 孔类型设置")
print("   - 功能: 设置炮孔为起始孔、死孔、失火孔或普通孔")
print("   - 使用: 选择类型后点击炮孔进行设置")

print("\n2. 孔径设置:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 孔径")
print("   - 功能: 设置炮孔直径和打孔设备")
print("   - 使用: 选择设备和孔径，支持单个或批量设置")

print("\n3. 岩石类型设置:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 岩石")
print("   - 功能: 设置炮孔所在岩石类型")
print("   - 使用: 选择岩石类型，自动计算炸药量")

print("\n4. 炸药类型设置:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 炸药")
print("   - 功能: 设置炮孔使用的炸药类型")
print("   - 使用: 选择炸药类型和装药结构")

print("\n5. 间排距设置:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 间排距")
print("   - 功能: 设置炮孔的间距和排距")
print("   - 使用: 输入间距和排距数值")

print("\n6. 连接成排:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 连接成排")
print("   - 功能: 将多个炮孔连接成排")
print("   - 使用: 依次点击要连接的炮孔")

print("\n7. 编辑炸药量:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 编辑炸药量")
print("   - 功能: 详细编辑炮孔的炸药参数")
print("   - 使用: 点击炮孔打开编辑对话框")

print("\n8. 作业面绘制:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 作业面")
print("   - 功能: 绘制爆破作业面边界")
print("   - 使用: 依次点击边界点，双击完成")

print("\n9. 边界线绘制:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 边界线")
print("   - 功能: 绘制安全边界线")
print("   - 使用: 依次点击边界点，双击完成")

print("\n10. 文本标注:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 文本")
print("   - 功能: 在图上添加文本标注")
print("   - 使用: 点击位置，输入文本内容")

print("\n11. 参考点添加:")
print("   - 菜单路径: 设计 -> 精细化设计 -> 参考点")
print("   - 功能: 添加测量参考点")
print("   - 使用: 点击位置或输入精确坐标")

print("\n" + "=" * 50)
print("所有功能都已完整实现，可以正常使用！")
