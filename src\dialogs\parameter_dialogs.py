# -*- coding: utf-8 -*-
"""
参数设置对话框
包含岩石参数、炸药参数、打孔设备参数的设置界面
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLineEdit, QComboBox, QPushButton, QTableWidget,
                            QTableWidgetItem, QHeaderView, QGroupBox, QSpinBox,
                            QDoubleSpinBox, QTextEdit, QTabWidget, QWidget,
                            QMessageBox, QLabel, QAbstractItemView)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

class RockParameterDialog(QDialog):
    """岩石参数设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("岩石参数设置")
        self.setModal(True)
        self.resize(1000, 600)
        self.init_ui()
        self.load_rock_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("岩石参数设置")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("注意：比重、松散系数、爆破漏斗、爆破阻力等范围值必须用英文\"-\"连接")
        info_label.setStyleSheet("color: #666; font-size: 9pt; margin: 5px;")
        layout.addWidget(info_label)
        
        # 岩石参数表格
        self.rock_table = QTableWidget()
        self.rock_table.setColumnCount(10)
        self.rock_table.setHorizontalHeaderLabels([
            "岩石名称", "岩石特征", "比重(kg/m³)", "松散系数", 
            "抗压强度(Mpa)", "抗拉强度(Mpa)", "爆破难度等级", 
            "修正系数Kr", "TNT当量q(kg/m³)", "气体爆破适用性"
        ])
        
        # 设置表格样式
        header = self.rock_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.rock_table.setAlternatingRowColors(True)
        self.rock_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        # 连接单元格编辑信号
        self.rock_table.itemChanged.connect(self.validate_cell_input)
        
        layout.addWidget(self.rock_table)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.add_button = QPushButton("添加岩石")
        self.edit_button = QPushButton("编辑选中")
        self.delete_button = QPushButton("删除选中")
        self.reset_button = QPushButton("重置数据")
        self.save_button = QPushButton("保存参数")
        self.cancel_button = QPushButton("取消")
        
        # 设置按钮样式
        for btn in [self.add_button, self.edit_button, self.delete_button]:
            btn.setStyleSheet("QPushButton { background-color: #e3f2fd; }")
        
        self.save_button.setStyleSheet("QPushButton { background-color: #4caf50; color: white; }")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.add_button.clicked.connect(self.add_rock_type)
        self.edit_button.clicked.connect(self.edit_rock_type)
        self.delete_button.clicked.connect(self.delete_rock_type)
        self.reset_button.clicked.connect(self.reset_data)
        self.save_button.clicked.connect(self.save_parameters)
        self.cancel_button.clicked.connect(self.reject)
    
    def load_rock_data(self):
        """从JSON文件加载岩石数据"""
        import json
        import os

        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            file_path = os.path.join(base_dir, 'data', 'rock_parameters.json')
            
            with open(file_path, 'r', encoding='utf-8') as f:
                rocks = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            QMessageBox.warning(self, "加载失败", f"无法加载岩石数据文件 'rock_parameters.json'。\n错误: {e}\n\n将使用内置的默认数据。")
            # Fallback to default data if file is missing or corrupt
            rocks = [
                {"岩石名称": "泥岩", "岩石特征": "软弱、高硬度、脆性", "比重(kg/m³)": "2800-3000", "松散系数": "1.5-1.9", "抗压强度(Mpa)": "200-350", "抗拉强度(Mpa)": "10-20", "爆破难度等级": "五级", "修正系数Kr": "0.6-0.7", "TNT当量q(kg/m³)": "0.65-0.75", "气体爆破适用性": "较难，需高压气体"},
                {"岩石名称": "花岗岩", "岩石特征": "均质、硬脆、磨耗性", "比重(kg/m³)": "2600-2800", "松散系数": "1.6-1.9", "抗压强度(Mpa)": "150-300", "抗拉强度(Mpa)": "7-20", "爆破难度等级": "五级", "修正系数Kr": "0.7-0.8", "TNT当量q(kg/m³)": "0.6-0.7", "气体爆破适用性": "较难，适合预裂"},
            ]

        self.rock_table.setRowCount(len(rocks))
        headers = [self.rock_table.horizontalHeaderItem(i).text() for i in range(self.rock_table.columnCount())]
        
        for row, rock_data in enumerate(rocks):
            for col, header in enumerate(headers):
                value = rock_data.get(header, "")
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter)
                self.rock_table.setItem(row, col, item)
    
    def validate_cell_input(self, item):
        """验证单元格输入格式"""
        if not item:
            return
        
        col = item.column()
        value = item.text().strip()
        
        # 需要验证范围格式的列
        range_columns = [2, 3, 4, 5, 7, 8]  # 更新为新列的索引
        
        if col in range_columns and value:
            # 验证范围格式：数字-数字 或 单个数字
            import re
            # 支持两种格式：单个数字 或 数字-数字
            pattern = r'^(\d+(\.\d+)?|\d+(\.\d+)?-\d+(\.\d+)?)$'
            
            if not re.match(pattern, value):
                # 只在格式完全错误时才提示，不要阻止用户输入
                self.show_format_hint(col, value)
                return False
        
        return True
    
    def show_format_hint(self, col, value):
        """显示格式提示（非阻塞）"""
        col_names = {
            2: "比重(kg/m³)", 3: "松散系数", 4: "抗压强度(Mpa)", 5: "抗拉强度(Mpa)",
            7: "修正系数Kr", 8: "TNT当量q(kg/m³)"
        }
        col_name = col_names.get(col, "参数")
        
        # 使用状态栏提示而不是弹窗
        if hasattr(self.parent(), 'update_status'):
            self.parent().update_status(f"{col_name}格式提示：请使用\"数字\"或\"数字-数字\"格式，如：2600-2800")
        else:
            print(f"格式提示：{col_name}应使用\"数字\"或\"数字-数字\"格式")
    
    def add_rock_type(self):
        """添加岩石类型"""
        row = self.rock_table.rowCount()
        self.rock_table.insertRow(row)
        
        # 设置默认值以匹配新的10列结构
        default_values = [
            "新岩石", "新特征", "2600-2800", "1.5-1.8",
            "100-150", "5-10", "三级", "1.0", "0.4-0.5", "中等"
        ]
        
        for col, value in enumerate(default_values):
            item = QTableWidgetItem(value)
            item.setTextAlignment(Qt.AlignCenter) # 保持居中对齐
            self.rock_table.setItem(row, col, item)
        
        # 选中新添加的行
        self.rock_table.selectRow(row)
        
        QMessageBox.information(self, "添加成功", "已添加新的岩石类型，请编辑相关参数")
    
    def edit_rock_type(self):
        """编辑岩石类型"""
        current_row = self.rock_table.currentRow()
        if current_row >= 0:
            rock_name = self.rock_table.item(current_row, 0).text()
            QMessageBox.information(self, "编辑提示", 
                                   f"请直接在表格中编辑\"{rock_name}\"的参数\n\n"
                                   f"注意：范围值必须用英文\"-\"连接")
        else:
            QMessageBox.warning(self, "未选择", "请先选择要编辑的岩石类型")
    
    def delete_rock_type(self):
        """删除岩石类型"""
        current_row = self.rock_table.currentRow()
        if current_row >= 0:
            rock_name = self.rock_table.item(current_row, 0).text()
            reply = QMessageBox.question(self, "确认删除", 
                                       f"确定要删除岩石类型\"{rock_name}\"吗？\n\n"
                                       f"此操作不可撤销！",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.rock_table.removeRow(current_row)
                QMessageBox.information(self, "删除成功", f"已删除岩石类型\"{rock_name}\"")
        else:
            QMessageBox.warning(self, "未选择", "请先选择要删除的岩石类型")
    
    def reset_data(self):
        """重置数据"""
        reply = QMessageBox.question(self, "确认重置", 
                                   "确定要重置所有岩石参数到默认值吗？\n\n"
                                   "此操作将清除所有自定义修改！",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.rock_table.setRowCount(0)
            self.load_rock_data()
            QMessageBox.information(self, "重置完成", "岩石参数已重置到默认值")
    
    def validate_all_data(self):
        """验证所有数据"""
        errors = []
        
        for row in range(self.rock_table.rowCount()):
            rock_name = self.rock_table.item(row, 0).text() if self.rock_table.item(row, 0) else ""
            
            if not rock_name.strip():
                errors.append(f"第{row+1}行：岩石名称不能为空")
                continue
            
            # 验证范围格式的列
            range_columns = [
                (2, "比重(kg/m³)"), (3, "松散系数"), (4, "抗压强度(Mpa)"), 
                (5, "抗拉强度(Mpa)"), (7, "修正系数Kr"), (8, "TNT当量q(kg/m³)")
            ]
            
            for col, col_name in range_columns:
                item = self.rock_table.item(row, col)
                if item:
                    value = item.text().strip()
                    if value:
                        import re
                        # 支持单个数字或数字范围格式
                        pattern = r'^(\d+(\.\d+)?|\d+(\.\d+)?-\d+(\.\d+)?)$'
                        if not re.match(pattern, value):
                            errors.append(f"第{row+1}行 {col_name}：格式错误，应为\"数字\"或\"数字-数字\"")
        
        return errors
    
    def save_parameters(self):
        """保存参数"""
        # 验证数据
        errors = self.validate_all_data()
        
        if errors:
            error_msg = "发现以下数据错误：\n\n" + "\n".join(errors[:5])
            if len(errors) > 5:
                error_msg += f"\n...还有{len(errors)-5}个错误"
            error_msg += "\n\n请修正后再保存！"
            QMessageBox.warning(self, "数据验证失败", error_msg)
            return
        
        # 收集数据
        rock_data = []
        rock_types = []
        for row in range(self.rock_table.rowCount()):
            row_data = {}
            for col in range(self.rock_table.columnCount()):
                item = self.rock_table.item(row, col)
                header = self.rock_table.horizontalHeaderItem(col).text()
                row_data[header] = item.text() if item else ""
            rock_data.append(row_data)
            
            # 收集岩石类型名称
            if row_data.get("岩石名称"):
                rock_types.append(row_data["岩石名称"])
        
        # 保存到数据库的逻辑
        self._save_to_database(rock_data)
        
        # 更新主窗口的岩石类型列表
        if self.parent() and hasattr(self.parent(), 'update_rock_types'):
            self.parent().update_rock_types(rock_types)
        
        # 通知其他功能模块参数已更新
        self._notify_parameter_update(rock_data)
        
        QMessageBox.information(self, "保存成功", 
                               f"已成功保存{len(rock_data)}种岩石参数！\n\n"
                               f"参数已应用到系统中，将影响爆破设计计算。")
        self.accept()
    
    def _save_to_database(self, rock_data):
        """保存数据到数据库"""
        # 这里实现实际的数据库保存逻辑
        # 可以使用SQLite、MySQL等数据库
        try:
            import json
            import os
            
            # 创建数据目录
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # 保存为JSON文件
            file_path = os.path.join(data_dir, 'rock_parameters.json')
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(rock_data, f, ensure_ascii=False, indent=2)
            
            print(f"岩石参数已保存到: {file_path}")
            
        except Exception as e:
            print(f"保存岩石参数时出错: {e}")
    
    def _notify_parameter_update(self, rock_data):
        """通知其他功能模块参数已更新"""
        # 通过信号机制通知其他模块
        if hasattr(self, 'parameters_updated'):
            self.parameters_updated.emit(rock_data)
        
        # 更新全局参数缓存
        global_params = {
            'rock_types': [item['岩石名称'] for item in rock_data if item.get('岩石名称')],
            'rock_data': rock_data,
            'update_time': __import__('datetime').datetime.now().isoformat()
        }
        
        # 保存全局参数
        try:
            import json
            import os
            
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            cache_file = os.path.join(data_dir, 'global_parameters.json')
            
            # 加载现有参数
            existing_params = {}
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    existing_params = json.load(f)
            
            # 更新岩石参数
            existing_params.update(global_params)
            
            # 保存更新后的参数
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(existing_params, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"更新全局参数时出错: {e}")

class ExplosiveParameterDialog(QDialog):
    """炸药参数设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("炸药参数设置")
        self.setModal(True)
        self.resize(1200, 600)
        self.init_ui()
        self.load_explosive_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("炸药参数设置")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("注意：比重、爆速、爆热等数值范围请用英文\"-\"连接")
        info_label.setStyleSheet("color: #666; font-size: 9pt; margin: 5px;")
        layout.addWidget(info_label)
        
        # 炸药参数表格
        self.explosive_table = QTableWidget()
        self.explosive_table.setColumnCount(7)
        self.explosive_table.setHorizontalHeaderLabels([
            "炸药名称", "炸药特征", "比重(kg/m³)", "爆速(m/s)", 
            "爆压(Gpa)", "TNT当量(%)", "应用场景"
        ])
        
        # 设置表格样式
        header = self.explosive_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.explosive_table.setAlternatingRowColors(True)
        self.explosive_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        # 连接单元格编辑信号
        self.explosive_table.itemChanged.connect(self.validate_cell_input)
        
        layout.addWidget(self.explosive_table)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.add_button = QPushButton("添加炸药")
        self.edit_button = QPushButton("编辑选中")
        self.delete_button = QPushButton("删除选中")
        self.reset_button = QPushButton("重置数据")
        self.save_button = QPushButton("保存参数")
        self.cancel_button = QPushButton("取消")
        
        # 设置按钮样式
        for btn in [self.add_button, self.edit_button, self.delete_button]:
            btn.setStyleSheet("QPushButton { background-color: #fff3e0; }")
        
        self.save_button.setStyleSheet("QPushButton { background-color: #4caf50; color: white; }")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_layout.addWidget(self.add_button)
        button_layout.addWidget(self.edit_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.add_button.clicked.connect(self.add_explosive_type)
        self.edit_button.clicked.connect(self.edit_explosive_type)
        self.delete_button.clicked.connect(self.delete_explosive_type)
        self.reset_button.clicked.connect(self.reset_data)
        self.save_button.clicked.connect(self.save_parameters)
        self.cancel_button.clicked.connect(self.reject)
    
    def load_explosive_data(self):
        """从JSON文件加载炸药数据"""
        import json
        import os

        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            file_path = os.path.join(base_dir, 'data', 'explosive_parameters.json')
            
            with open(file_path, 'r', encoding='utf-8') as f:
                explosives = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            QMessageBox.warning(self, "加载失败", f"无法加载炸药数据文件 'explosive_parameters.json'。\n错误: {e}\n\n将使用内置的默认数据。")
            explosives = [
                {"炸药名称": "ANFO", "炸药特征": "低成本、低爆速、需雷管助爆、不抗水", "比重(kg/m³)": "800-900", "爆速(m/s)": "2500-3500", "爆压(Gpa)": "2-3", "TNT当量(%)": "80-85", "应用场景": "露天矿采场、松软土石方工程"},
                {"炸药名称": "乳化炸药", "炸药特征": "高爆速、抗水性强、可泵送、稳定性好", "比重(kg/m³)": "1200-1300", "爆速(m/s)": "4500-5500", "爆压(Gpa)": "8-10", "TNT当量(%)": "100-120", "应用场景": "硬岩钻孔、地下爆破"},
            ]

        self.explosive_table.setRowCount(len(explosives))
        headers = [self.explosive_table.horizontalHeaderItem(i).text() for i in range(self.explosive_table.columnCount())]

        for row, explosive_data in enumerate(explosives):
            for col, header in enumerate(headers):
                value = explosive_data.get(header, "")
                item = QTableWidgetItem(str(value))
                item.setTextAlignment(Qt.AlignCenter) # 居中显示
                self.explosive_table.setItem(row, col, item)
    
    def validate_cell_input(self, item):
        """验证单元格输入格式"""
        if not item:
            return
        
        col = item.column()
        value = item.text().strip()
        
        # 需要验证范围格式的列：比重、爆速、爆压、TNT当量
        range_columns = [2, 3, 4, 5]  # 对应的列索引
        
        if col in range_columns and value and value != "-":
            # 验证范围格式：数字-数字 或 单个数字
            import re
            # 允许单个数字或范围格式
            pattern = r'^(\d+(\.\d+)?|\d+(\.\d+)?-\d+(\.\d+)?)$'
            
            if not re.match(pattern, value):
                QMessageBox.warning(self, "输入格式错误", 
                                   f"数值必须是数字或用英文\"-\"连接的范围！\n"
                                   f"正确格式示例：1600 或 750-1000\n"
                                   f"您输入的是：{value}")
                
                # 恢复到之前的值或设置默认值
                default_values = {
                    2: "1000-1200",    # 比重
                    3: "3000-4000",    # 爆速
                    4: "5.0-8.0",      # 爆压
                    5: "90-100"         # TNT当量
                }
                item.setText(default_values.get(col, ""))
                return False
        
        return True
    
    def add_explosive_type(self):
        """添加炸药类型"""
        row = self.explosive_table.rowCount()
        self.explosive_table.insertRow(row)
        
        # 设置默认值
        default_values = [
            "新炸药", "新特征", "1000-1200", "3000-4000", 
            "5-8", "90-100", "通用场景"
        ]
        
        for col, value in enumerate(default_values):
            item = QTableWidgetItem(value)
            item.setTextAlignment(Qt.AlignCenter) # 居中显示
            self.explosive_table.setItem(row, col, item)
        
        # 选中新添加的行
        self.explosive_table.selectRow(row)
        
        QMessageBox.information(self, "添加成功", "已添加新的炸药类型，请编辑相关参数")
    
    def edit_explosive_type(self):
        """编辑炸药类型"""
        current_row = self.explosive_table.currentRow()
        if current_row >= 0:
            explosive_name = self.explosive_table.item(current_row, 0).text()
            QMessageBox.information(self, "编辑提示", 
                                   f"请直接在表格中编辑\"{explosive_name}\"的参数\n\n"
                                   f"注意：数值范围用英文\"-\"连接")
        else:
            QMessageBox.warning(self, "未选择", "请先选择要编辑的炸药类型")
    
    def delete_explosive_type(self):
        """删除炸药类型"""
        current_row = self.explosive_table.currentRow()
        if current_row >= 0:
            explosive_name = self.explosive_table.item(current_row, 0).text()
            reply = QMessageBox.question(self, "确认删除", 
                                       f"确定要删除炸药类型\"{explosive_name}\"吗？\n\n"
                                       f"此操作不可撤销！",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.explosive_table.removeRow(current_row)
                QMessageBox.information(self, "删除成功", f"已删除炸药类型\"{explosive_name}\"")
        else:
            QMessageBox.warning(self, "未选择", "请先选择要删除的炸药类型")
    
    def reset_data(self):
        """重置数据"""
        reply = QMessageBox.question(self, "确认重置", 
                                   "确定要重置所有炸药参数到默认值吗？\n\n"
                                   "此操作将清除所有自定义修改！",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.explosive_table.setRowCount(0)
            self.load_explosive_data()
            QMessageBox.information(self, "重置完成", "炸药参数已重置到默认值")
    
    def validate_all_data(self):
        """验证所有数据"""
        errors = []
        
        for row in range(self.explosive_table.rowCount()):
            explosive_name = self.explosive_table.item(row, 0).text() if self.explosive_table.item(row, 0) else ""
            
            if not explosive_name.strip():
                errors.append(f"第{row+1}行：炸药名称不能为空")
                continue
            
            # 验证数值格式的列
            numeric_columns = [(2, "比重(kg/m³)"), (3, "爆速(m/s)"), (4, "爆压(Gpa)"), (5, "TNT当量(%)")]
            
            for col, col_name in numeric_columns:
                item = self.explosive_table.item(row, col)
                if item:
                    value = item.text().strip()
                    if value and value != "-":
                        import re
                        pattern = r'^(\d+(\.\d+)?|\d+(\.\d+)?-\d+(\.\d+)?)$'
                        if not re.match(pattern, value):
                            errors.append(f"第{row+1}行 {col_name}：格式错误，应为数字或\"数字-数字\"")
        
        return errors
    
    def save_parameters(self):
        """保存参数"""
        # 验证数据
        errors = self.validate_all_data()
        
        if errors:
            error_msg = "发现以下数据错误：\n\n" + "\n".join(errors[:5])
            if len(errors) > 5:
                error_msg += f"\n...还有{len(errors)-5}个错误"
            error_msg += "\n\n请修正后再保存！"
            QMessageBox.warning(self, "数据验证失败", error_msg)
            return
        
        # 收集数据
        explosive_data = []
        explosive_types = []
        for row in range(self.explosive_table.rowCount()):
            row_data = {}
            for col in range(self.explosive_table.columnCount()):
                item = self.explosive_table.item(row, col)
                header = self.explosive_table.horizontalHeaderItem(col).text()
                row_data[header] = item.text() if item else ""
            explosive_data.append(row_data)
            
            # 收集炸药类型名称
            if row_data.get("炸药名称"):
                explosive_types.append(row_data["炸药名称"])
        
        # 保存到数据库的逻辑
        self._save_to_database(explosive_data)
        
        # 更新主窗口的炸药类型列表
        if self.parent() and hasattr(self.parent(), 'update_explosive_types'):
            self.parent().update_explosive_types(explosive_types)
        
        # 通知其他功能模块参数已更新
        self._notify_parameter_update(explosive_data, explosive_types)
        
        QMessageBox.information(self, "保存成功", 
                               f"已成功保存{len(explosive_data)}种炸药参数！\n\n"
                               f"参数已应用到系统中，将影响爆破设计计算。")
        self.accept()
    
    def _save_to_database(self, explosive_data):
        """保存数据到数据库"""
        try:
            import json
            import os
            
            # 创建数据目录
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # 保存为JSON文件
            file_path = os.path.join(data_dir, 'explosive_parameters.json')
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(explosive_data, f, ensure_ascii=False, indent=2)
            
            print(f"炸药参数已保存到: {file_path}")
            
        except Exception as e:
            print(f"保存炸药参数时出错: {e}")
    
    def _notify_parameter_update(self, explosive_data, explosive_types):
        """通知其他功能模块参数已更新"""
        # 更新全局参数缓存
        global_params = {
            'explosive_types': explosive_types,
            'explosive_data': explosive_data,
            'update_time': __import__('datetime').datetime.now().isoformat()
        }
        
        # 保存全局参数
        try:
            import json
            import os
            
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            cache_file = os.path.join(data_dir, 'global_parameters.json')
            
            # 加载现有参数
            existing_params = {}
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    existing_params = json.load(f)
            
            # 更新炸药参数
            existing_params.update(global_params)
            
            # 保存更新后的参数
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(existing_params, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"更新全局参数时出错: {e}")

class DrillingEquipmentDialog(QDialog):
    """打孔设备参数对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("打孔设备参数设置")
        self.setModal(True)
        self.resize(900, 600)
        self.init_ui()
        self.load_equipment_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("打孔设备参数设置")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明标签
        info_label = QLabel("用户预设各种打孔设备的参数，包括名称、价格、安全距离、孔径和适用场景")
        info_label.setStyleSheet("color: #666; font-size: 9pt; margin: 5px;")
        layout.addWidget(info_label)
        
        # 设备列表表格
        self.equipment_table = QTableWidget()
        self.equipment_table.setColumnCount(5)
        self.equipment_table.setHorizontalHeaderLabels([
            "设备名称", "价格(元/m)", "安全距离(m)", "孔径(mm)", "适用场景"
        ])
        self.equipment_table.verticalHeader().setVisible(False)
        
        # 设置表格样式
        header = self.equipment_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.equipment_table.setAlternatingRowColors(True)
        self.equipment_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        
        # 设置行高
        self.equipment_table.verticalHeader().setDefaultSectionSize(40)
        
        # 启用表格编辑
        self.equipment_table.setEditTriggers(QAbstractItemView.DoubleClicked | 
                                           QAbstractItemView.EditKeyPressed)
        
        # 连接单元格编辑信号
        self.equipment_table.itemChanged.connect(self.validate_cell_input)
        
        layout.addWidget(self.equipment_table)
        
        # 操作区域
        operation_layout = QVBoxLayout()
        
        # 操作按钮
        button_group1 = QHBoxLayout()
        
        self.insert_button = QPushButton("插入设备")
        self.insert_button.setStyleSheet("QPushButton { background-color: #e8f5e8; }")
        
        self.delete_button = QPushButton("删除设备")
        self.delete_button.setStyleSheet("QPushButton { background-color: #ffe8e8; }")
        
        self.move_up_button = QPushButton("上移")
        self.move_up_button.setStyleSheet("QPushButton { background-color: #e8f0ff; }")
        
        self.move_down_button = QPushButton("下移")
        self.move_down_button.setStyleSheet("QPushButton { background-color: #e8f0ff; }")
        
        self.edit_button = QPushButton("编辑设备")
        self.edit_button.setStyleSheet("QPushButton { background-color: #fff8e1; }")
        
        button_group1.addWidget(self.insert_button)
        button_group1.addWidget(self.delete_button)
        button_group1.addWidget(self.move_up_button)
        button_group1.addWidget(self.move_down_button)
        button_group1.addWidget(self.edit_button)
        button_group1.addStretch()
        
        operation_layout.addLayout(button_group1)
        
        # 保存/取消按钮
        button_group2 = QHBoxLayout()
        
        self.save_button = QPushButton("保存参数")
        self.save_button.setStyleSheet("QPushButton { background-color: #4caf50; color: white; font-weight: bold; }")
        
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_group2.addStretch()
        button_group2.addWidget(self.save_button)
        button_group2.addWidget(self.cancel_button)
        
        operation_layout.addLayout(button_group2)
        layout.addLayout(operation_layout)
        
        # 连接信号
        self.insert_button.clicked.connect(self.insert_equipment)
        self.delete_button.clicked.connect(self.delete_equipment)
        self.move_up_button.clicked.connect(self.move_up)
        self.move_down_button.clicked.connect(self.move_down)
        self.edit_button.clicked.connect(self.edit_equipment)
        self.save_button.clicked.connect(self.save_parameters)
        self.cancel_button.clicked.connect(self.reject)
    
    def load_equipment_data(self):
        """加载打孔设备数据"""
        from PyQt5.QtWidgets import QLineEdit
        equipment_list = [
            ("YGZ-90凿岩机", "45.0", "5.0", "110", "中硬岩层、隧道掘进"),
            ("YT-28气腿式凿岩机", "35.0", "3.0", "90", "小型矿山、巷道钻孔"),
            ("KY-150潜孔钻机", "80.0", "8.0", "150", "露天矿山、深孔爆破"),
            ("CM-351履带钻机", "120.0", "10.0", "200", "大型露天爆破、高台阶作业"),
            ("手持式电钻", "25.0", "2.0", "75", "浅孔爆破、二次破碎"),
            ("多功能钻机", "95.0", "7.0", "165", "复杂地形、多孔径需求"),
            ("液压凿岩机", "65.0", "6.0", "125", "高效自动化作业"),
            ("气动钻机", "55.0", "4.0", "90", "轻型工程、移动灵活")
        ]
        
        self.equipment_table.setRowCount(len(equipment_list))
        for row, equipment_data in enumerate(equipment_list):
            for col, value in enumerate(equipment_data):
                if col == 3:  # 孔径(mm)列
                    combo = QComboBox()
                    combo.addItems(["75", "90", "110", "125", "150", "165", "200", "250", "300"])
                    combo.setEditable(True)
                    combo.setCurrentText(str(value))
                    
                    # 设置下拉框文本居中
                    line_edit = QLineEdit()
                    line_edit.setAlignment(Qt.AlignCenter)
                    combo.setLineEdit(line_edit)
                    
                    # 连接下拉框变化信号
                    combo.currentTextChanged.connect(self.validate_diameter_input)
                    self.equipment_table.setCellWidget(row, col, combo)
                else:
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)
                    self.equipment_table.setItem(row, col, item)
    
    def validate_cell_input(self, item):
        """验证单元格输入"""
        if not item:
            return
        
        col = item.column()
        value = item.text().strip()
        
        # 验证价格列
        if col == 1:  # 价格列
            try:
                price = float(value)
                if price < 0:
                    QMessageBox.warning(self, "输入错误", "价格不能为负数！")
                    item.setText("50.0")
                    return False
            except ValueError:
                QMessageBox.warning(self, "输入错误", "价格必须是数字！\n正确格式示例：45.0")
                item.setText("50.0")
                return False
        
        # 验证安全距离列
        elif col == 2:  # 安全距离列
            try:
                distance = float(value)
                if distance < 0:
                    QMessageBox.warning(self, "输入错误", "安全距离不能为负数！")
                    item.setText("5.0")
                    return False
            except ValueError:
                QMessageBox.warning(self, "输入错误", "安全距离必须是数字！\n正确格式示例：5.0")
                item.setText("5.0")
                return False
        
        return True
    
    def validate_diameter_input(self, text):
        """验证孔径输入"""
        try:
            diameter = float(text)
            if diameter <= 0:
                QMessageBox.warning(self, "输入错误", "孔径必须大于0！")
                sender = self.sender()
                if sender:
                    sender.setCurrentText("110")
                return False
        except ValueError:
            QMessageBox.warning(self, "输入错误", "孔径必须是数字！\n正确格式示例：110")
            sender = self.sender()
            if sender:
                sender.setCurrentText("110")
            return False
        return True
    
    def insert_equipment(self):
        """插入设备行"""
        from PyQt5.QtWidgets import QLineEdit
        current_row = self.equipment_table.currentRow()
        insert_row = current_row + 1 if current_row >= 0 else self.equipment_table.rowCount()
        
        self.equipment_table.insertRow(insert_row)
        
        # 设置默认值
        default_values = [
            "新设备", "50.0", "5.0", "110", "通用场景"
        ]
        
        for col, value in enumerate(default_values):
            if col == 3:  # 孔径列
                combo = QComboBox()
                combo.addItems(["75", "90", "110", "125", "150", "165", "200", "250", "300"])
                combo.setEditable(True)
                combo.setCurrentText(value)
                
                # 设置下拉框文本居中
                line_edit = QLineEdit()
                line_edit.setAlignment(Qt.AlignCenter)
                combo.setLineEdit(line_edit)

                combo.currentTextChanged.connect(self.validate_diameter_input)
                self.equipment_table.setCellWidget(insert_row, col, combo)
            else:
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                self.equipment_table.setItem(insert_row, col, item)
        
        # 选中新插入的行
        self.equipment_table.selectRow(insert_row)
        
        QMessageBox.information(self, "添加成功", "已插入新的设备记录，请编辑相关参数")
    
    def delete_equipment(self):
        """删除设备行"""
        current_row = self.equipment_table.currentRow()
        if current_row >= 0:
            equipment_name_item = self.equipment_table.item(current_row, 0)
            equipment_name = equipment_name_item.text() if equipment_name_item else f"第{current_row + 1}行设备"
            reply = QMessageBox.question(self, "确认删除", 
                                       f"确定要删除设备\"{equipment_name}\"吗？\n\n"
                                       f"此操作不可撤销！",
                                       QMessageBox.Yes | QMessageBox.No,
                                       QMessageBox.No)
            if reply == QMessageBox.Yes:
                self.equipment_table.removeRow(current_row)
                QMessageBox.information(self, "删除成功", f"已删除设备\"{equipment_name}\"")
        else:
            QMessageBox.warning(self, "未选择", "请先选择要删除的设备行")
    
    def edit_equipment(self):
        """编辑设备"""
        current_row = self.equipment_table.currentRow()
        if current_row >= 0:
            equipment_name_item = self.equipment_table.item(current_row, 0)
            equipment_name = equipment_name_item.text() if equipment_name_item else f"第{current_row + 1}行设备"
            QMessageBox.information(self, "编辑提示", 
                                   f"请直接在表格中编辑\"{equipment_name}\"的参数\n\n"
                                   f"• 双击单元格或按F2键开始编辑\n"
                                   f"• 孔径可通过下拉框选择或直接输入\n"
                                   f"• 价格和安全距离必须为正数")
        else:
            QMessageBox.warning(self, "未选择", "请先选择要编辑的设备行")
    
    def move_up(self):
        """上移行"""
        current_row = self.equipment_table.currentRow()
        if current_row > 0:
            self.swap_rows(current_row, current_row - 1)
            self.equipment_table.selectRow(current_row - 1)
    
    def move_down(self):
        """下移行"""
        current_row = self.equipment_table.currentRow()
        if current_row >= 0 and current_row < self.equipment_table.rowCount() - 1:
            self.swap_rows(current_row, current_row + 1)
            self.equipment_table.selectRow(current_row + 1)
    
    def swap_rows(self, row1, row2):
        """交换两行"""
        for col in range(self.equipment_table.columnCount()):
            if col == 3:  # 孔径列（下拉框）
                widget1 = self.equipment_table.cellWidget(row1, col)
                widget2 = self.equipment_table.cellWidget(row2, col)
                
                if widget1 and widget2:
                    text1 = widget1.currentText()
                    text2 = widget2.currentText()
                    widget1.setCurrentText(text2)
                    widget2.setCurrentText(text1)
            else:
                item1 = self.equipment_table.takeItem(row1, col)
                item2 = self.equipment_table.takeItem(row2, col)
                
                if item1:
                    item1.setTextAlignment(Qt.AlignCenter)
                    self.equipment_table.setItem(row2, col, item1)
                if item2:
                    item2.setTextAlignment(Qt.AlignCenter)
                    self.equipment_table.setItem(row1, col, item2)
    
    def validate_all_data(self):
        """验证所有数据"""
        errors = []
        
        for row in range(self.equipment_table.rowCount()):
            # 检查设备名称
            name_item = self.equipment_table.item(row, 0)
            if not name_item or not name_item.text().strip():
                errors.append(f"第{row+1}行：设备名称不能为空")
                continue
            
            # 检查价格
            price_item = self.equipment_table.item(row, 1)
            if price_item:
                try:
                    price = float(price_item.text())
                    if price < 0:
                        errors.append(f"第{row+1}行：价格不能为负数")
                except ValueError:
                    errors.append(f"第{row+1}行：价格格式错误，必须是数字")
            
            # 检查安全距离
            distance_item = self.equipment_table.item(row, 2)
            if distance_item:
                try:
                    distance = float(distance_item.text())
                    if distance < 0:
                        errors.append(f"第{row+1}行：安全距离不能为负数")
                except ValueError:
                    errors.append(f"第{row+1}行：安全距离格式错误，必须是数字")
            
            # 检查孔径
            diameter_widget = self.equipment_table.cellWidget(row, 3)
            if diameter_widget:
                try:
                    diameter = float(diameter_widget.currentText())
                    if diameter <= 0:
                        errors.append(f"第{row+1}行：孔径必须大于0")
                except ValueError:
                    errors.append(f"第{row+1}行：孔径格式错误，必须是数字")
        
        return errors
    
    def save_parameters(self):
        """保存参数"""
        # 验证数据
        errors = self.validate_all_data()
        
        if errors:
            error_msg = "发现以下数据错误：\n\n" + "\n".join(errors[:5])
            if len(errors) > 5:
                error_msg += f"\n...还有{len(errors)-5}个错误"
            error_msg += "\n\n请修正后再保存！"
            QMessageBox.warning(self, "数据验证失败", error_msg)
            return
        
        # 收集数据
        equipment_data = []
        headers = [self.equipment_table.horizontalHeaderItem(i).text() for i in range(self.equipment_table.columnCount())]
        
        for row in range(self.equipment_table.rowCount()):
            row_data = {}
            for col, header in enumerate(headers):
                if col == 3: # 孔径列 (ComboBox)
                    widget = self.equipment_table.cellWidget(row, col)
                    row_data[header] = widget.currentText() if widget else ""
                else: # 其他文本列
                    item = self.equipment_table.item(row, col)
                    row_data[header] = item.text() if item else ""
            equipment_data.append(row_data)
        
        # 保存到数据库的逻辑
        self._save_to_database(equipment_data)
        
        # 更新主窗口的设备列表
        if self.parent() and hasattr(self.parent(), 'update_drilling_equipment'):
            self.parent().update_drilling_equipment(equipment_data)
        
        # 通知其他功能模块参数已更新
        self._notify_parameter_update(equipment_data)
        
        QMessageBox.information(self, "保存成功", 
                              f"已成功保存{len(equipment_data)}种打孔设备参数！\n\n"
                              f"参数已应用到系统中，将影响钻孔成本计算。")
        self.accept()
    
    def _save_to_database(self, equipment_data):
        """保存数据到数据库"""
        try:
            import json
            import os
            
            # 创建数据目录
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # 保存为JSON文件
            file_path = os.path.join(data_dir, 'drilling_equipment_parameters.json')
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(equipment_data, f, ensure_ascii=False, indent=2)
            
            print(f"打孔设备参数已保存到: {file_path}")
            
        except Exception as e:
            print(f"保存打孔设备参数时出错: {e}")
    
    def _notify_parameter_update(self, equipment_data):
        """通知其他功能模块参数已更新"""
        # 更新全局参数缓存
        global_params = {
            'drilling_equipment': equipment_data,
            'update_time': __import__('datetime').datetime.now().isoformat()
        }
        
        # 保存全局参数
        try:
            import json
            import os
            
            data_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
            cache_file = os.path.join(data_dir, 'global_parameters.json')
            
            # 加载现有参数
            existing_params = {}
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    existing_params = json.load(f)
            
            # 更新设备参数
            existing_params.update(global_params)
            
            # 保存更新后的参数
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(existing_params, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"更新全局参数时出错: {e}")
    
