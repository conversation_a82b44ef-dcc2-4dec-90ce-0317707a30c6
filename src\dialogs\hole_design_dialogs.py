# -*- coding: utf-8 -*-
"""
炮孔设计相关对话框
包括炮孔编辑、坐标输入、AVV设置等对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
                            QLabel, QLineEdit, QDoubleSpinBox, QSpinBox, 
                            QPushButton, QComboBox, QGroupBox, QGridLayout,
                            QMessageBox, QTabWidget, QWidget)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QDoubleValidator, QIntValidator

class CoordinateInputDialog(QDialog):
    """坐标输入对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加炮孔")
        self.setModal(True)
        self.resize(300, 200)
        self.init_ui()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("通过坐标添加炮孔")
        title_label.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 坐标输入
        form_layout = QFormLayout()
        
        self.x_spin = QDoubleSpinBox()
        self.x_spin.setRange(-9999.0, 9999.0)
        self.x_spin.setDecimals(2)
        self.x_spin.setSuffix(" m")
        form_layout.addRow("X坐标:", self.x_spin)
        
        self.y_spin = QDoubleSpinBox()
        self.y_spin.setRange(-9999.0, 9999.0)
        self.y_spin.setDecimals(2)
        self.y_spin.setSuffix(" m")
        form_layout.addRow("Y坐标:", self.y_spin)
        
        self.z_spin = QDoubleSpinBox()
        self.z_spin.setRange(-500.0, 5000.0)
        self.z_spin.setValue(100.0)
        self.z_spin.setDecimals(2)
        self.z_spin.setSuffix(" m")
        form_layout.addRow("Z坐标:", self.z_spin)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        
        self.ok_button.setStyleSheet("QPushButton { background-color: #4caf50; color: white; }")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
    
    def get_coordinates(self):
        """获取坐标"""
        return (self.x_spin.value(), self.y_spin.value(), self.z_spin.value())

class HoleEditDialog(QDialog):
    """炮孔编辑对话框"""
    
    def __init__(self, hole_data=None, parent=None):
        super().__init__(parent)
        self.hole_data = hole_data or {}
        self.setWindowTitle("炮孔编辑")
        self.setModal(True)
        self.resize(400, 500)
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("炮孔参数编辑")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 基本信息选项卡
        basic_tab = self.create_basic_tab()
        tab_widget.addTab(basic_tab, "基本信息")
        
        # 坐标信息选项卡
        coord_tab = self.create_coordinate_tab()
        tab_widget.addTab(coord_tab, "坐标信息")
        
        # AVV参数选项卡
        avv_tab = self.create_avv_tab()
        tab_widget.addTab(avv_tab, "AVV参数")
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.reset_button = QPushButton("重置")
        
        self.ok_button.setStyleSheet("QPushButton { background-color: #4caf50; color: white; }")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.ok_button.clicked.connect(self.accept_changes)
        self.cancel_button.clicked.connect(self.reject)
        self.reset_button.clicked.connect(self.reset_data)
    
    def create_basic_tab(self):
        """创建基本信息选项卡"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 炮孔编号
        self.hole_id_edit = QLineEdit()
        self.hole_id_edit.setPlaceholderText("如: H001")
        layout.addRow("炮孔编号:", self.hole_id_edit)
        
        # 设计孔深
        self.depth_spin = QDoubleSpinBox()
        self.depth_spin.setRange(1.0, 100.0)
        self.depth_spin.setValue(15.0)
        self.depth_spin.setDecimals(1)
        self.depth_spin.setSuffix(" m")
        layout.addRow("设计孔深:", self.depth_spin)
        
        # 孔径
        self.diameter_spin = QDoubleSpinBox()
        self.diameter_spin.setRange(50.0, 300.0)
        self.diameter_spin.setValue(110.0)
        self.diameter_spin.setDecimals(1)
        self.diameter_spin.setSuffix(" mm")
        layout.addRow("孔径:", self.diameter_spin)
        
        return widget
    
    def create_coordinate_tab(self):
        """创建坐标信息选项卡"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # X坐标
        self.x_coord_spin = QDoubleSpinBox()
        self.x_coord_spin.setRange(-9999.0, 9999.0)
        self.x_coord_spin.setValue(0.0)
        self.x_coord_spin.setDecimals(2)
        self.x_coord_spin.setSuffix(" m")
        layout.addRow("X坐标:", self.x_coord_spin)
        
        # Y坐标
        self.y_coord_spin = QDoubleSpinBox()
        self.y_coord_spin.setRange(-9999.0, 9999.0)
        self.y_coord_spin.setValue(0.0)
        self.y_coord_spin.setDecimals(2)
        self.y_coord_spin.setSuffix(" m")
        layout.addRow("Y坐标:", self.y_coord_spin)
        
        # Z坐标（高程）
        self.z_coord_spin = QDoubleSpinBox()
        self.z_coord_spin.setRange(-500.0, 5000.0)
        self.z_coord_spin.setValue(100.0)
        self.z_coord_spin.setDecimals(2)
        self.z_coord_spin.setSuffix(" m")
        layout.addRow("Z坐标(高程):", self.z_coord_spin)
        
        return widget
    
    def create_avv_tab(self):
        """创建AVV参数选项卡"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 间距 (A)
        self.spacing_spin = QDoubleSpinBox()
        self.spacing_spin.setRange(1.0, 20.0)
        self.spacing_spin.setValue(3.5)
        self.spacing_spin.setDecimals(1)
        self.spacing_spin.setSuffix(" m")
        layout.addRow("间距(A):", self.spacing_spin)
        
        # 排距 (V)
        self.row_spacing_spin = QDoubleSpinBox()
        self.row_spacing_spin.setRange(1.0, 20.0)
        self.row_spacing_spin.setValue(4.0)
        self.row_spacing_spin.setDecimals(1)
        self.row_spacing_spin.setSuffix(" m")
        layout.addRow("排距(V):", self.row_spacing_spin)
        
        # 段高 (V)
        self.bench_height_spin = QDoubleSpinBox()
        self.bench_height_spin.setRange(1.0, 50.0)
        self.bench_height_spin.setValue(15.0)
        self.bench_height_spin.setDecimals(1)
        self.bench_height_spin.setSuffix(" m")
        layout.addRow("段高(V):", self.bench_height_spin)
        
        return widget
    
    def load_data(self):
        """加载数据到界面"""
        if not self.hole_data:
            return
        
        # 基本信息
        self.hole_id_edit.setText(self.hole_data.get('hole_id', ''))
        
        self.depth_spin.setValue(self.hole_data.get('depth', 15.0))
        self.diameter_spin.setValue(self.hole_data.get('diameter', 110.0))
        
        # 坐标信息
        self.x_coord_spin.setValue(self.hole_data.get('x', 0.0))
        self.y_coord_spin.setValue(self.hole_data.get('y', 0.0))
        self.z_coord_spin.setValue(self.hole_data.get('z', 100.0))
        
        # AVV参数
        self.spacing_spin.setValue(self.hole_data.get('spacing', 3.5))
        self.row_spacing_spin.setValue(self.hole_data.get('row_spacing', 4.0))
        self.bench_height_spin.setValue(self.hole_data.get('bench_height', 15.0))
    
    def get_data(self):
        """获取界面数据"""
        return {
            # 基本信息
            'hole_id': self.hole_id_edit.text().strip(),
            'depth': self.depth_spin.value(),
            'diameter': self.diameter_spin.value(),
            
            # 坐标信息
            'x': self.x_coord_spin.value(),
            'y': self.y_coord_spin.value(),
            'z': self.z_coord_spin.value(),
            
            # AVV参数
            'spacing': self.spacing_spin.value(),
            'row_spacing': self.row_spacing_spin.value(),
            'bench_height': self.bench_height_spin.value()
        }
    
    def validate_data(self):
        """验证数据"""
        hole_id = self.hole_id_edit.text().strip()
        if not hole_id:
            QMessageBox.warning(self, "数据验证", "炮孔编号不能为空！")
            return False
        return True
    
    def accept_changes(self):
        """确认修改"""
        if self.validate_data():
            self.accept()
    
    def reset_data(self):
        """重置数据"""
        reply = QMessageBox.question(self, "确认重置", 
                                   "确定要重置所有参数到默认值吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.hole_data = {}
            self.load_data()

class AVVParameterDialog(QDialog):
    """AVV参数设置对话框"""
    
    def __init__(self, current_params=None, parent=None):
        super().__init__(parent)
        self.current_params = current_params or {}
        self.setWindowTitle("AVV参数设置")
        self.setModal(True)
        self.resize(350, 300)
        self.init_ui()
        self.load_data()
    
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("AVV参数设置")
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel("设置间距、排距、段高等爆破参数")
        info_label.setStyleSheet("color: #666; font-size: 9pt;")
        info_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(info_label)
        
        # 参数输入
        form_layout = QFormLayout()
        
        # 间距
        self.spacing_spin = QDoubleSpinBox()
        self.spacing_spin.setRange(1.0, 20.0)
        self.spacing_spin.setValue(3.5)
        self.spacing_spin.setDecimals(1)
        self.spacing_spin.setSuffix(" m")
        form_layout.addRow("间距(A):", self.spacing_spin)
        
        # 排距
        self.row_spacing_spin = QDoubleSpinBox()
        self.row_spacing_spin.setRange(1.0, 20.0)
        self.row_spacing_spin.setValue(4.0)
        self.row_spacing_spin.setDecimals(1)
        self.row_spacing_spin.setSuffix(" m")
        form_layout.addRow("排距(V):", self.row_spacing_spin)
        
        # 段高
        self.bench_height_spin = QDoubleSpinBox()
        self.bench_height_spin.setRange(1.0, 50.0)
        self.bench_height_spin.setValue(15.0)
        self.bench_height_spin.setDecimals(1)
        self.bench_height_spin.setSuffix(" m")
        form_layout.addRow("段高(V):", self.bench_height_spin)
        
        layout.addLayout(form_layout)
        
        # 按钮
        button_layout = QHBoxLayout()
        
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        self.reset_button = QPushButton("重置")
        
        self.ok_button.setStyleSheet("QPushButton { background-color: #4caf50; color: white; }")
        self.cancel_button.setStyleSheet("QPushButton { background-color: #f44336; color: white; }")
        
        button_layout.addWidget(self.reset_button)
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button.clicked.connect(self.reject)
        self.reset_button.clicked.connect(self.reset_data)
    
    def load_data(self):
        """加载数据"""
        self.spacing_spin.setValue(self.current_params.get('spacing', 3.5))
        self.row_spacing_spin.setValue(self.current_params.get('row_spacing', 4.0))
        self.bench_height_spin.setValue(self.current_params.get('bench_height', 15.0))
    
    def get_parameters(self):
        """获取参数"""
        return {
            'spacing': self.spacing_spin.value(),
            'row_spacing': self.row_spacing_spin.value(),
            'bench_height': self.bench_height_spin.value()
        }
    
    def reset_data(self):
        """重置数据"""
        self.spacing_spin.setValue(3.5)
        self.row_spacing_spin.setValue(4.0)
        self.bench_height_spin.setValue(15.0)

class AssignDrillingEquipmentDialog(QDialog):
    """设置打孔设备对话框"""
    def __init__(self, equipment_list, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择打孔设备")
        self.drilling_equipment = equipment_list
        self.selection = {}

        self.init_ui()
        self.connect_signals()
        self.on_equipment_change(0) # Load initial value

    def init_ui(self):
        """初始化界面"""
        from PyQt5.QtWidgets import QFormLayout, QComboBox, QDoubleSpinBox, QHBoxLayout, QPushButton
        
        layout = QVBoxLayout(self)
        form_layout = QFormLayout()

        # 打孔设备下拉框
        self.equipment_combo = QComboBox()
        equipment_names = [eq.get("设备名称", "未知设备") for eq in self.drilling_equipment]
        self.equipment_combo.addItems(equipment_names)
        form_layout.addRow("打孔设备:", self.equipment_combo)

        # 孔径输入框
        self.diameter_spin = QDoubleSpinBox()
        self.diameter_spin.setRange(30.0, 500.0)
        self.diameter_spin.setValue(110.0)
        self.diameter_spin.setSuffix(" mm")
        self.diameter_spin.setDecimals(2)
        form_layout.addRow("孔径(mm):", self.diameter_spin)
        
        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("确定")
        self.cancel_button = QPushButton("取消")
        button_layout.addStretch()
        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        layout.addLayout(button_layout)

    def connect_signals(self):
        """连接信号"""
        self.equipment_combo.currentIndexChanged.connect(self.on_equipment_change)
        self.ok_button.clicked.connect(self.accept_selection)
        self.cancel_button.clicked.connect(self.reject)

    def on_equipment_change(self, index):
        """当设备选择变化时更新孔径"""
        if 0 <= index < len(self.drilling_equipment):
            selected_eq = self.drilling_equipment[index]
            diameter_str = selected_eq.get("孔径", "110.0")
            try:
                diameter_val = float(diameter_str)
                self.diameter_spin.setValue(diameter_val)
            except (ValueError, TypeError):
                self.diameter_spin.setValue(110.0)

    def accept_selection(self):
        """保存选择并接受对话框"""
        self.selection = {
            'equipment_name': self.equipment_combo.currentText(),
            'diameter': self.diameter_spin.value()
        }
        self.accept()

    def get_selection(self):
        """获取用户选择"""
        return self.selection 